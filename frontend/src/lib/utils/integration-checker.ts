/**
 * Integration Checker Utility
 *
 * Utilities for checking user's connected integrations and determining
 * which prompts they can run based on their MCP connections.
 */

import { createClient } from '@/lib/supabase/client';

export interface UserIntegrations {
  gmail: boolean;
  google_sheets: boolean;
  google_calendar: boolean;
  google_docs: boolean;
  google_drive: boolean;
  notion: boolean;
  slack: boolean;
  teams: boolean;
  twitter: boolean;
  linear: boolean;
  clickup: boolean;
  hubspot: boolean;
  salesforce: boolean;
  airtable: boolean;
  zoom: boolean;
  outlook: boolean;
  reddit: boolean;
  findanyone: boolean; // Always true - built-in tool
}

export interface IntegrationRequirement {
  app_key: string;
  name: string;
  required: boolean;
}

/**
 * Check which integrations a user has connected by examining their default agent's custom_mcps
 * Includes retry logic for post-OAuth scenarios
 */
export async function getUserConnectedIntegrations(retryCount: number = 0): Promise<UserIntegrations> {
  try {
    const supabase = createClient();
    const { data: { session } } = await supabase.auth.getSession();

    if (!session) {
      // Return all false if not authenticated
      return {
        gmail: false,
        google_sheets: false,
        google_calendar: false,
        google_docs: false,
        google_drive: false,
        notion: false,
        slack: false,
        teams: false,
        twitter: false,
        linear: false,
        clickup: false,
        hubspot: false,
        salesforce: false,
        airtable: false,
        zoom: false,
        outlook: false,
        reddit: false,
        findanyone: true, // Always available
      };
    }

    // Get user's account ID from basejump schema
    const { data: accountData, error: accountError } = await supabase
      .schema('basejump')
      .from('accounts')
      .select('id')
      .eq('primary_owner_user_id', session.user.id)
      .eq('personal_account', true)
      .single();

    if (accountError || !accountData) {
      console.error('Error fetching account:', accountError);
      return {
        gmail: false,
        google_sheets: false,
        google_calendar: false,
        google_docs: false,
        google_drive: false,
        notion: false,
        slack: false,
        teams: false,
        twitter: false,
        linear: false,
        clickup: false,
        hubspot: false,
        salesforce: false,
        airtable: false,
        zoom: false,
        outlook: false,
        reddit: false,
        findanyone: true,
      };
    }

    // Get default agent's custom_mcps with retry logic for post-OAuth
    const { data: agentData, error: agentError } = await supabase
      .from('agents')
      .select('custom_mcps')
      .eq('account_id', accountData.id)
      .eq('is_default', true)
      .single();

    if (agentError || !agentData) {
      // If this is a retry attempt and we still can't get data, check if we should retry
      if (retryCount < 2) {
        console.log(`Retrying integration check (attempt ${retryCount + 1}/3)...`);
        await new Promise(resolve => setTimeout(resolve, 2000)); // Wait 2 seconds
        return getUserConnectedIntegrations(retryCount + 1);
      }

      console.error('Error fetching default agent after retries:', agentError);
      return {
        gmail: false,
        google_sheets: false,
        google_calendar: false,
        google_docs: false,
        google_drive: false,
        notion: false,
        slack: false,
        teams: false,
        twitter: false,
        linear: false,
        clickup: false,
        hubspot: false,
        salesforce: false,
        airtable: false,
        zoom: false,
        outlook: false,
        reddit: false,
        findanyone: true,
      };
    }

    // Parse custom_mcps to check for connected integrations
    const customMcps = agentData.custom_mcps || [];
    const connectedAppKeys = new Set(
      customMcps.map((mcp: any) => mcp.app_key).filter(Boolean)
    );

    return {
      gmail: connectedAppKeys.has('gmail'),
      google_sheets: connectedAppKeys.has('google_sheets'),
      google_calendar: connectedAppKeys.has('google_calendar'),
      google_docs: connectedAppKeys.has('google_docs'),
      google_drive: connectedAppKeys.has('google_drive'),
      notion: connectedAppKeys.has('notion'),
      slack: connectedAppKeys.has('slack'),
      teams: connectedAppKeys.has('teams'),
      twitter: connectedAppKeys.has('twitter'),
      linear: connectedAppKeys.has('linear'),
      clickup: connectedAppKeys.has('clickup'),
      hubspot: connectedAppKeys.has('hubspot'),
      salesforce: connectedAppKeys.has('salesforce'),
      airtable: connectedAppKeys.has('airtable'),
      zoom: connectedAppKeys.has('zoom'),
      outlook: connectedAppKeys.has('outlook'),
      reddit: connectedAppKeys.has('reddit'),
      findanyone: true, // Always available
    };

  } catch (error) {
    console.error('Error checking user integrations:', error);
    return {
      gmail: false,
      google_sheets: false,
      google_calendar: false,
      google_docs: false,
      google_drive: false,
      notion: false,
      slack: false,
      teams: false,
      twitter: false,
      linear: false,
      clickup: false,
      hubspot: false,
      salesforce: false,
      airtable: false,
      zoom: false,
      outlook: false,
      reddit: false,
      findanyone: true,
    };
  }
}

/**
 * Check if user has all required integrations for a specific set of requirements
 */
export function hasRequiredIntegrations(
  userIntegrations: UserIntegrations,
  requirements: IntegrationRequirement[]
): boolean {
  return requirements.every(req => {
    if (!req.required) return true;

    switch (req.app_key) {
      case 'gmail':
        return userIntegrations.gmail;
      case 'google_sheets':
        return userIntegrations.google_sheets;
      case 'google_calendar':
        return userIntegrations.google_calendar;
      case 'google_docs':
        return userIntegrations.google_docs;
      case 'google_drive':
        return userIntegrations.google_drive;
      case 'notion':
        return userIntegrations.notion;
      case 'slack':
        return userIntegrations.slack;
      case 'teams':
        return userIntegrations.teams;
      case 'twitter':
        return userIntegrations.twitter;
      case 'linear':
        return userIntegrations.linear;
      case 'clickup':
        return userIntegrations.clickup;
      case 'hubspot':
        return userIntegrations.hubspot;
      case 'salesforce':
        return userIntegrations.salesforce;
      case 'airtable':
        return userIntegrations.airtable;
      case 'zoom':
        return userIntegrations.zoom;
      case 'outlook':
        return userIntegrations.outlook;
      case 'reddit':
        return userIntegrations.reddit;
      case 'findanyone':
        return userIntegrations.findanyone;
      default:
        return false;
    }
  });
}

/**
 * Get missing integrations for a specific set of requirements
 */
export function getMissingIntegrations(
  userIntegrations: UserIntegrations,
  requirements: IntegrationRequirement[]
): IntegrationRequirement[] {
  return requirements.filter(req => {
    if (!req.required) return false;

    switch (req.app_key) {
      case 'gmail':
        return !userIntegrations.gmail;
      case 'google_sheets':
        return !userIntegrations.google_sheets;
      case 'google_calendar':
        return !userIntegrations.google_calendar;
      case 'google_docs':
        return !userIntegrations.google_docs;
      case 'google_drive':
        return !userIntegrations.google_drive;
      case 'notion':
        return !userIntegrations.notion;
      case 'slack':
        return !userIntegrations.slack;
      case 'teams':
        return !userIntegrations.teams;
      case 'twitter':
        return !userIntegrations.twitter;
      case 'linear':
        return !userIntegrations.linear;
      case 'clickup':
        return !userIntegrations.clickup;
      case 'hubspot':
        return !userIntegrations.hubspot;
      case 'salesforce':
        return !userIntegrations.salesforce;
      case 'airtable':
        return !userIntegrations.airtable;
      case 'zoom':
        return !userIntegrations.zoom;
      case 'outlook':
        return !userIntegrations.outlook;
      case 'reddit':
        return !userIntegrations.reddit;
      case 'findanyone':
        return !userIntegrations.findanyone;
      default:
        return true; // Unknown integration is considered missing
    }
  });
}

/**
 * Force refresh integrations with retry logic - useful for post-OAuth scenarios
 */
export async function refreshUserIntegrations(): Promise<UserIntegrations> {
  console.log('Force refreshing user integrations...');
  return getUserConnectedIntegrations(0); // Start with retry count 0
}

/**
 * Get the current user's email address from Supabase auth
 */
export async function getUserEmail(): Promise<string | null> {
  try {
    const supabase = createClient();
    const { data: { session } } = await supabase.auth.getSession();

    if (!session?.user?.email) {
      return null;
    }

    return session.user.email;
  } catch (error) {
    console.error('Error fetching user email:', error);
    return null;
  }
}

/**
 * Integration display names mapping
 */
export const INTEGRATION_DISPLAY_NAMES: Record<string, string> = {
  gmail: 'Gmail',
  google_sheets: 'Google Sheets',
  google_calendar: 'Google Calendar',
  google_docs: 'Google Docs',
  google_drive: 'Google Drive',
  notion: 'Notion',
  slack: 'Slack',
  teams: 'Microsoft Teams',
  twitter: 'Twitter',
  linear: 'Linear',
  clickup: 'ClickUp',
  hubspot: 'HubSpot',
  salesforce: 'Salesforce',
  airtable: 'Airtable',
  zoom: 'Zoom',
  outlook: 'Outlook',
  reddit: 'Reddit',
  findanyone: 'FindAnyone',
};
