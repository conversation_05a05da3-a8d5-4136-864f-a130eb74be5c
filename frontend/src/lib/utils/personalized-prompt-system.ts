/**
 * Personalized Prompt Recommendation System
 *
 * Core system that orchestrates checking user integrations, personalizing prompts,
 * and determining which prompts to show based on user's connected integrations.
 */

import {
  getUserConnectedIntegrations,
  refreshUserIntegrations,
  getUserEmail,
  UserIntegrations,
  IntegrationRequirement
} from './integration-checker';
import {
  INITIAL_PROMPTS,
  ADVANCED_PROMPTS,
  PersonalizedPrompt,
  personalizePromptQuery,
  getAvailablePrompts,
  getUnavailablePrompts,
  getMissingIntegrationsForPrompt,
  sortPromptsByPriority
} from './personalized-prompts';

export interface PersonalizedPromptRecommendation {
  prompt: PersonalizedPrompt;
  personalizedQuery: string;
  canRun: boolean;
  missingIntegrations: IntegrationRequirement[];
}

export interface PromptRecommendationResult {
  userEmail: string | null;
  userIntegrations: UserIntegrations;
  availablePrompts: PersonalizedPromptRecommendation[];
  unavailablePrompts: PersonalizedPromptRecommendation[];
  hasAnyIntegrations: boolean;
  showInitialPrompts: boolean;
}

/**
 * Main function to get personalized prompt recommendations for a user
 */
export async function getPersonalizedPromptRecommendations(): Promise<PromptRecommendationResult> {
  try {
    // Fetch user data in parallel
    const [userEmail, userIntegrations] = await Promise.all([
      getUserEmail(),
      getUserConnectedIntegrations()
    ]);

    // Check if user has Gmail connected to move beyond initial prompts
    const hasGmail = userIntegrations.gmail;
    const hasCoreIntegrations = hasGmail;

    // Determine if user has any third-party integrations (excluding findanyone which is built-in)
    const hasAnyIntegrations = Object.entries(userIntegrations)
      .filter(([key]) => key !== 'findanyone')
      .some(([, connected]) => connected);

    // Determine which prompts to show based on user's integration status
    let promptsToEvaluate: PersonalizedPrompt[];

    if (!hasCoreIntegrations) {
      // User hasn't connected Gmail yet - show initial prompts only
      promptsToEvaluate = INITIAL_PROMPTS;
    } else {
      // User has Gmail connected - show advanced prompts that they can potentially use
      // Filter advanced prompts to only show ones where user has at least one required integration
      const relevantAdvancedPrompts = ADVANCED_PROMPTS.filter(prompt => {
        return prompt.integrations.some(integration => {
          if (!integration.required) return true;
          return userIntegrations[integration.app_key as keyof UserIntegrations] === true;
        });
      });

      // Combine initial prompts with relevant advanced prompts, limit to 6 total
      promptsToEvaluate = [...INITIAL_PROMPTS, ...relevantAdvancedPrompts].slice(0, 6);
    }

    const showInitialPrompts = !hasCoreIntegrations;

    // Create recommendations for each prompt
    const recommendations: PersonalizedPromptRecommendation[] = promptsToEvaluate.map(prompt => {
      const personalizedQuery = userEmail ? personalizePromptQuery(prompt.query, userEmail) : prompt.query;
      const missingIntegrations = getMissingIntegrationsForPrompt(prompt, userIntegrations);
      const canRun = missingIntegrations.length === 0;

      return {
        prompt,
        personalizedQuery,
        canRun,
        missingIntegrations,
      };
    });

    // Separate available and unavailable prompts
    const availablePrompts = recommendations.filter(rec => rec.canRun);
    const unavailablePrompts = recommendations.filter(rec => !rec.canRun);

    // Sort by priority
    const sortedAvailablePrompts = sortPromptsByPriority(availablePrompts.map(r => r.prompt))
      .map(prompt => recommendations.find(r => r.prompt.id === prompt.id)!)
      .filter(Boolean);

    const sortedUnavailablePrompts = sortPromptsByPriority(unavailablePrompts.map(r => r.prompt))
      .map(prompt => recommendations.find(r => r.prompt.id === prompt.id)!)
      .filter(Boolean);

    return {
      userEmail,
      userIntegrations,
      availablePrompts: sortedAvailablePrompts,
      unavailablePrompts: sortedUnavailablePrompts,
      hasAnyIntegrations,
      showInitialPrompts,
    };

  } catch (error) {
    console.error('Error getting personalized prompt recommendations:', error);

    // Return safe fallback
    return {
      userEmail: null,
      userIntegrations: {
        gmail: false,
        google_sheets: false,
        google_calendar: false,
        google_docs: false,
        google_drive: false,
        notion: false,
        slack: false,
        teams: false,
        twitter: false,
        linear: false,
        clickup: false,
        hubspot: false,
        salesforce: false,
        airtable: false,
        zoom: false,
        outlook: false,
        reddit: false,
        findanyone: true,
      },
      availablePrompts: [],
      unavailablePrompts: [],
      hasAnyIntegrations: false,
      showInitialPrompts: true,
    };
  }
}

/**
 * Get the prompts that should be displayed to the user
 * This determines the main logic of what prompts to show
 */
export function getPromptsToDisplay(result: PromptRecommendationResult): PersonalizedPromptRecommendation[] {
  // If user has no integrations, show all initial prompts (they'll need to connect integrations)
  if (!result.hasAnyIntegrations) {
    return [...result.availablePrompts, ...result.unavailablePrompts];
  }

  // If user has some integrations, show available prompts first, then unavailable ones
  return [...result.availablePrompts, ...result.unavailablePrompts];
}

/**
 * Check if a specific prompt requires integrations that the user doesn't have
 */
export function promptRequiresMissingIntegrations(
  prompt: PersonalizedPrompt,
  userIntegrations: UserIntegrations
): boolean {
  const missingIntegrations = getMissingIntegrationsForPrompt(prompt, userIntegrations);
  return missingIntegrations.length > 0;
}

/**
 * Get app keys for missing integrations (used for filtering the connection modal)
 */
export function getMissingIntegrationAppKeys(
  missingIntegrations: IntegrationRequirement[]
): string[] {
  return missingIntegrations.map(integration => integration.app_key);
}

/**
 * Force refresh prompt recommendations after OAuth - uses retry logic for integrations
 */
export async function getPersonalizedPromptRecommendationsPostOAuth(): Promise<PromptRecommendationResult> {
  try {
    console.log('Getting post-OAuth prompt recommendations with retry logic...');

    // Fetch user data with retry logic for post-OAuth scenarios
    const [userEmail, userIntegrations] = await Promise.all([
      getUserEmail(),
      refreshUserIntegrations() // This includes retry logic
    ]);

    // Check if user has Gmail connected to move beyond initial prompts
    const hasGmail = userIntegrations.gmail;
    const hasCoreIntegrations = hasGmail;

    // Determine if user has any third-party integrations (excluding findanyone which is built-in)
    const hasAnyIntegrations = Object.entries(userIntegrations)
      .filter(([key]) => key !== 'findanyone')
      .some(([, connected]) => connected);

    // Determine which prompts to show based on user's integration status
    let promptsToEvaluate: PersonalizedPrompt[];

    if (!hasCoreIntegrations) {
      // User hasn't connected Gmail yet - show initial prompts only
      promptsToEvaluate = INITIAL_PROMPTS;
    } else {
      // User has Gmail connected - show advanced prompts that they can potentially use
      // Filter advanced prompts to only show ones where user has at least one required integration
      const relevantAdvancedPrompts = ADVANCED_PROMPTS.filter(prompt => {
        return prompt.integrations.some(integration => {
          if (!integration.required) return true;
          return userIntegrations[integration.app_key as keyof UserIntegrations] === true;
        });
      });

      // Combine initial prompts with relevant advanced prompts, limit to 6 total
      promptsToEvaluate = [...INITIAL_PROMPTS, ...relevantAdvancedPrompts].slice(0, 6);
    }

    const showInitialPrompts = !hasCoreIntegrations;

    // Create recommendations for each prompt
    const recommendations: PersonalizedPromptRecommendation[] = promptsToEvaluate.map(prompt => {
      const personalizedQuery = userEmail ? personalizePromptQuery(prompt.query, userEmail) : prompt.query;
      const missingIntegrations = getMissingIntegrationsForPrompt(prompt, userIntegrations);
      const canRun = missingIntegrations.length === 0;

      return {
        prompt,
        personalizedQuery,
        canRun,
        missingIntegrations,
      };
    });

    // Separate available and unavailable prompts
    const availablePrompts = recommendations.filter(rec => rec.canRun);
    const unavailablePrompts = recommendations.filter(rec => !rec.canRun);

    // Sort by priority
    const sortedAvailablePrompts = sortPromptsByPriority(availablePrompts.map(r => r.prompt))
      .map(prompt => recommendations.find(r => r.prompt.id === prompt.id)!)
      .filter(Boolean);

    const sortedUnavailablePrompts = sortPromptsByPriority(unavailablePrompts.map(r => r.prompt))
      .map(prompt => recommendations.find(r => r.prompt.id === prompt.id)!)
      .filter(Boolean);

    return {
      userEmail,
      userIntegrations,
      availablePrompts: sortedAvailablePrompts,
      unavailablePrompts: sortedUnavailablePrompts,
      hasAnyIntegrations,
      showInitialPrompts,
    };

  } catch (error) {
    console.error('Error getting post-OAuth personalized prompt recommendations:', error);

    // Return safe fallback
    return {
      userEmail: null,
      userIntegrations: {
        gmail: false,
        google_sheets: false,
        google_calendar: false,
        google_docs: false,
        google_drive: false,
        notion: false,
        slack: false,
        teams: false,
        twitter: false,
        linear: false,
        clickup: false,
        hubspot: false,
        salesforce: false,
        airtable: false,
        zoom: false,
        outlook: false,
        reddit: false,
        findanyone: true,
      },
      availablePrompts: [],
      unavailablePrompts: [],
      hasAnyIntegrations: false,
      showInitialPrompts: true,
    };
  }
}
