'use client';

import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Workflow, Play, Plus, ArrowRight, ExternalLink } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { useFeatureFlag } from '@/lib/feature-flags';
import { ComposioApp } from '@/types/composio';
import { ComposioMCPService } from '@/lib/composio-api';
import { MCPAppsModal } from './mcp-apps-modal';
import { createClient } from '@/lib/supabase/client';
import { toast } from 'sonner';
import { useQueryClient } from '@tanstack/react-query';
import { agentKeys } from '@/hooks/react-query/agents/keys';

interface ActionCardsProps {
  className?: string;
}

interface ActionCardProps {
  icon: React.ReactNode;
  title: string;
  subtitle?: string;
  onClick: () => void;
  actionIcon?: React.ReactNode;
  width?: string;
  glow?: boolean;
}

function ActionCard({ icon, title, subtitle, onClick, actionIcon, width = "w-52", glow = false }: ActionCardProps) {
  return (
    <motion.div
      whileHover={{
        transition: { duration: 0.2, ease: "easeOut" }
      }}
      className={`flex-shrink-0 ${glow ? 'relative' : ''}`}
    >
      <Card
        className={`${width} h-14 cursor-pointer transition-all duration-200 shadow-none dark:shadow-sm dark:hover:shadow-lg bg-transparent dark:bg-muted/30 border hover:border-border/80 ${glow ? 'border-slate-400 dark:border-white/20' : 'border-border'}`}
        onClick={onClick}
      >
        <CardContent className="px-4 h-full flex items-center justify-between">
          {/* Left: Icon + Text */}
          <div className="flex items-center gap-3 min-w-0 flex-1">
            {/* Action Icon */}
            <div className="w-7 h-7 flex-shrink-0 flex items-center justify-center bg-primary/10 border border-primary/20 rounded-md">
              {icon}
            </div>

            {/* Text Content */}
            <div className="min-w-0 flex-1">
              <div className="font-medium text-sm text-foreground leading-tight">
                {title}
              </div>
              {subtitle && (
                <div className="text-xs text-muted-foreground">
                  {subtitle}
                </div>
              )}
            </div>
          </div>

          {/* Right: Action Button */}
          <div className="flex items-center flex-shrink-0 ml-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={(e) => {
                e.stopPropagation();
                onClick();
              }}
              className="h-7 w-7 p-0 rounded-full hover:bg-primary/10 group"
            >
              {actionIcon || <ArrowRight className="h-4 w-4 text-muted-foreground group-hover:text-primary transition-colors" />}
            </Button>
          </div>
        </CardContent>
      </Card>
    </motion.div>
  );
}

export function ActionCards({ className }: ActionCardsProps) {
  const router = useRouter();
  const queryClient = useQueryClient();
  const { enabled: workflowsEnabled } = useFeatureFlag("workflows");

  // MCP Apps Modal State
  const [apps, setApps] = useState<ComposioApp[]>([]);
  const [loading, setLoading] = useState(true);
  const [connectingApps, setConnectingApps] = useState<Set<string>>(new Set());
  const [disconnectingApps, setDisconnectingApps] = useState<Set<string>>(new Set());
  const [connectedApps, setConnectedApps] = useState<Set<string>>(new Set());
  const [isModalOpen, setIsModalOpen] = useState(false);



  // Helper function to get authenticated headers
  const getAuthHeaders = async () => {
    const supabase = createClient();
    const { data: { session } } = await supabase.auth.getSession();

    if (!session?.access_token) {
      throw new Error('No authentication token available. Please sign in.');
    }

    return {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${session.access_token}`,
    };
  };

  // Load supported apps using v3 API
  useEffect(() => {
    const loadApps = async () => {
      try {
        setLoading(true);

        const data = await ComposioMCPService.getSupportedApps();

        if (data.success) {
          setApps(data.apps);
        } else {
          throw new Error('Failed to load supported integrations');
        }
      } catch (error) {
        console.error('Error loading apps:', error);
        toast.error("Failed to load MCP servers", {
          description: error instanceof Error ? error.message : 'Unknown error',
        });
      } finally {
        setLoading(false);
      }
    };

    loadApps();
  }, []);

  // Load existing connections
  useEffect(() => {
    const loadConnections = async () => {
      try {
        const connections = await ComposioMCPService.listUserConnections();
        const connectedAppKeys: Set<string> = new Set(
          connections.map((conn: any) => conn.app_key as string)
        );
        setConnectedApps(connectedAppKeys);
      } catch (error) {
        console.error('Error loading connections:', error);
      }
    };

    loadConnections();
  }, []);

  // Optimistic polling for faster connection detection
  const startOptimisticPolling = (appKey: string, appName: string) => {
    let attempts = 0;
    const maxAttempts = 20; // 2 minutes max

    const pollStatus = async () => {
      attempts++;

      try {
        const connectionRequestId = localStorage.getItem('composio_connection_request_id');
        if (!connectionRequestId) return; // User cancelled or cleared

        const statusResult = await ComposioMCPService.checkConnectionStatus(
          connectionRequestId,
          appKey as any
        );

        if (statusResult.success && statusResult.is_connected) {
          // Success! Clear storage and update UI
          localStorage.removeItem('composio_recently_connected');
          localStorage.removeItem('composio_connection_request_id');
          localStorage.removeItem('composio_connection_app_name');

          // Update connected apps immediately (optimistic update)
          setConnectedApps(prev => new Set(prev).add(appKey));

          // Reload connections and invalidate cache
          const connections = await ComposioMCPService.listUserConnections();
          const connectedAppKeys: Set<string> = new Set(
            connections.map((conn: any) => conn.app_key as string)
          );
          setConnectedApps(connectedAppKeys);

          queryClient.invalidateQueries({ queryKey: agentKeys.lists() });
          queryClient.invalidateQueries({ queryKey: agentKeys.defaultMCPs() });

          toast.success("Authentication complete");

          return; // Stop polling
        }

        // Continue polling if not connected yet and under max attempts
        if (attempts < maxAttempts) {
          // Exponential backoff: start fast, slow down over time
          const delay = Math.min(1000 + attempts * 500, 6000);
          setTimeout(pollStatus, delay);
        } else {
          // Max attempts reached, clear storage silently
          localStorage.removeItem('composio_recently_connected');
          localStorage.removeItem('composio_connection_request_id');
          localStorage.removeItem('composio_connection_app_name');
        }

      } catch (error) {
        console.error('Error during optimistic polling:', error);

        if (attempts < maxAttempts) {
          // Retry with longer delay on error
          setTimeout(pollStatus, 3000);
        }
      }
    };

    // Start polling immediately, then after short delay
    setTimeout(pollStatus, 500);
  };

  // Handle MCP server connection - simplified v3 flow
  const handleConnect = async (appKey: string, appName: string) => {
    if (connectingApps.has(appKey) || connectedApps.has(appKey)) return;

    // Show instant connecting state
    setConnectingApps(prev => new Set(prev).add(appKey));

    // Show simple authenticating toast
    toast("Authenticating...");

    try {
      // Step 1: Initiate connection and get redirect URL immediately
      const initResult = await ComposioMCPService.initiateConnection(appKey);

      // Step 2: Store flags for post-OAuth handling
      localStorage.setItem('composio_recently_connected', appKey);
      localStorage.setItem('composio_connection_request_id', initResult.connection_request_id);
      localStorage.setItem('composio_connection_app_name', appName);

      // Step 3: Use full page redirect for all browsers to avoid cross-origin issues
      window.location.href = initResult.redirect_url;

      // Start optimistic polling
      startOptimisticPolling(appKey, appName);

    } catch (error: any) {
      console.error('Connection error:', error);
      // Silently fail - no error toasts
    } finally {
      setConnectingApps(prev => {
        const newSet = new Set(prev);
        newSet.delete(appKey);
        return newSet;
      });
    }
  };

  // Handle viewing tools for connected MCP servers - simplified
  const handleViewTools = async (appKey: string, appName: string) => {
    try {
      const connection = await ComposioMCPService.getConnectionStatus(appKey);
      // No toasts - just check silently
    } catch (error: any) {
      console.error('Error viewing connection:', error);
      // No error toasts
    }
  };

  // Handle MCP server disconnection - simplified
  const handleDisconnect = async (appKey: string, appName: string) => {
    if (disconnectingApps.has(appKey) || !connectedApps.has(appKey)) return;

    setDisconnectingApps(prev => new Set(prev).add(appKey));

    try {
      // Optimistic update - remove from UI immediately
      setConnectedApps(prev => {
        const newSet = new Set(prev);
        newSet.delete(appKey);
        return newSet;
      });

      // Call the v3 delete endpoint to remove the connection
      const success = await ComposioMCPService.deleteConnection(appKey);

      if (success) {
        // Invalidate React Query cache to refresh cursor agent selector
        queryClient.invalidateQueries({ queryKey: agentKeys.lists() });
        queryClient.invalidateQueries({ queryKey: agentKeys.defaultMCPs() });
      } else {
        // Rollback optimistic update on failure
        setConnectedApps(prev => new Set(prev).add(appKey));
      }

    } catch (error: any) {
      console.error('Disconnect error:', error);
      // Rollback optimistic update on error
      setConnectedApps(prev => new Set(prev).add(appKey));
    } finally {
      setDisconnectingApps(prev => {
        const newSet = new Set(prev);
        newSet.delete(appKey);
        return newSet;
      });
    }
  };

  const handleCreateWorkflow = () => {
    router.push('/workflows');
  };



  const handleAllApps = () => {
    setIsModalOpen(true);
  };

  return (
    <>
            <div className={className}>

        {/* Header */}
        <div className="flex justify-between items-center mb-4">
          <span className="text-sm text-muted-foreground">
            quick actions
          </span>
        </div>

        {/* Cards container */}
        <div className="flex gap-4 overflow-x-auto scrollbar-hide">
          {/* All Apps Card - First position */}
          <ActionCard
            icon={<ExternalLink className="h-5 w-5 text-primary" />}
            title="All the apps you need"
            onClick={handleAllApps}
            width="w-72"
          />

          {/* Workflow Creation Card - Only show if workflows feature flag is enabled */}
          {workflowsEnabled && (
            <ActionCard
              icon={<Workflow className="h-5 w-5 text-primary" />}
              title="Create workflow"
              onClick={handleCreateWorkflow}
              actionIcon={<Plus className="h-4 w-4 text-muted-foreground group-hover:text-primary transition-colors" />}
              width="w-60"
            />
          )}


        </div>
      </div>

      {/* MCP Apps Modal */}
      <MCPAppsModal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        apps={apps}
        connectedApps={connectedApps}
        connectingApps={connectingApps}
        disconnectingApps={disconnectingApps}
        onConnect={handleConnect}
        onDisconnect={handleDisconnect}
        onViewTools={handleViewTools}
        isLoading={loading}
      />


    </>
  );
}
