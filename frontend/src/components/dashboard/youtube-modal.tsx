'use client';

import React, { useState, useRef, useEffect, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { X, Play } from 'lucide-react';
import { Button } from '@/components/ui/button';
import Image from 'next/image';

interface YouTubeModalProps {
  isOpen: boolean;
  onClose: () => void;
  videoId: string;
  title?: string;
}

export function YouTubeModal({ isOpen, onClose, videoId, title = "Short coffee-chat with your new co-worker" }: YouTubeModalProps) {
  const [isPlaying, setIsPlaying] = useState(false);
  const [showCustomOverlay, setShowCustomOverlay] = useState(true);
  const iframeRef = useRef<HTMLIFrameElement>(null);
  const modalRef = useRef<HTMLDivElement>(null);

  // Extract video ID from URL if full URL is provided
  const extractVideoId = (url: string): string => {
    if (url.includes('youtube.com/watch?v=')) {
      return url.split('v=')[1].split('&')[0];
    }
    if (url.includes('youtu.be/')) {
      return url.split('youtu.be/')[1].split('?')[0];
    }
    return url; // Assume it's already a video ID
  };

  const cleanVideoId = extractVideoId(videoId);

  // Handle play button click
  const handlePlay = () => {
    setIsPlaying(true);
    setShowCustomOverlay(false);

    // Send play command to iframe
    if (iframeRef.current) {
      iframeRef.current.src = `https://www.youtube.com/embed/${cleanVideoId}?autoplay=1&rel=0&modestbranding=1&playsinline=1`;
    }
  };

  // Handle modal close
  const handleClose = useCallback(() => {
    setIsPlaying(false);
    setShowCustomOverlay(true);

    // Stop video by resetting iframe src
    if (iframeRef.current) {
      iframeRef.current.src = `https://www.youtube.com/embed/${cleanVideoId}?rel=0&modestbranding=1&playsinline=1`;
    }

    onClose();
  }, [cleanVideoId, onClose]);

  // Handle click outside modal
  const handleBackdropClick = (e: React.MouseEvent) => {
    if (modalRef.current && !modalRef.current.contains(e.target as Node)) {
      handleClose();
    }
  };

  // Handle escape key
  useEffect(() => {
    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === 'Escape' && isOpen) {
        handleClose();
      }
    };

    if (isOpen) {
      document.addEventListener('keydown', handleEscape);
      document.body.style.overflow = 'hidden';
    }

    return () => {
      document.removeEventListener('keydown', handleEscape);
      document.body.style.overflow = 'unset';
    };
  }, [isOpen, handleClose]);

  // Reset state when modal opens
  useEffect(() => {
    if (isOpen) {
      setIsPlaying(false);
      setShowCustomOverlay(true);
    }
  }, [isOpen]);

  return (
    <AnimatePresence>
      {isOpen && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          transition={{ duration: 0.2 }}
          className="fixed inset-0 z-50 flex items-center justify-center p-4 bg-black/80 backdrop-blur-sm"
          onClick={handleBackdropClick}
        >
          <motion.div
            ref={modalRef}
            initial={{ scale: 0.9, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            exit={{ scale: 0.9, opacity: 0 }}
            transition={{ duration: 0.2, ease: "easeOut" }}
            className="relative w-full max-w-4xl mx-auto bg-background rounded-lg shadow-2xl overflow-hidden"
          >
            {/* Header */}
            <div className="flex items-center justify-between p-4 border-b border-border">
              <h2 className="text-lg font-semibold text-foreground">{title}</h2>
              <Button
                variant="ghost"
                size="sm"
                onClick={handleClose}
                className="h-8 w-8 p-0 rounded-full hover:bg-muted"
              >
                <X className="h-4 w-4" />
              </Button>
            </div>

            {/* Video Container */}
            <div className="relative bg-black">
              {/* Responsive 16:9 aspect ratio container */}
              <div className="relative w-full" style={{ paddingBottom: '56.25%' }}>
                {/* YouTube iframe */}
                <iframe
                  ref={iframeRef}
                  src={`https://www.youtube.com/embed/${cleanVideoId}?rel=0&modestbranding=1&playsinline=1`}
                  title={title}
                  className="absolute inset-0 w-full h-full"
                  frameBorder="0"
                  allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"
                  allowFullScreen
                />

                {/* Custom Play Button Overlay */}
                <AnimatePresence>
                  {showCustomOverlay && (
                    <motion.div
                      initial={{ opacity: 1 }}
                      exit={{ opacity: 0 }}
                      transition={{ duration: 0.3 }}
                      className="absolute inset-0 flex items-center justify-center bg-black/20 cursor-pointer"
                      onClick={handlePlay}
                    >
                      {/* Video Thumbnail Overlay */}
                      <div className="absolute inset-0">
                        <Image
                          src={`https://img.youtube.com/vi/${cleanVideoId}/maxresdefault.jpg`}
                          alt="Video thumbnail"
                          fill
                          className="object-cover"
                          onError={(e) => {
                            // Fallback to standard quality thumbnail
                            (e.target as HTMLImageElement).src = `https://img.youtube.com/vi/${cleanVideoId}/hqdefault.jpg`;
                          }}
                        />
                        <div className="absolute inset-0 bg-black/30" />
                      </div>

                      {/* Play Button */}
                      <motion.div
                        whileHover={{ scale: 1.1 }}
                        whileTap={{ scale: 0.95 }}
                        className="relative z-10 flex items-center justify-center w-20 h-20 bg-primary/90 hover:bg-primary rounded-full shadow-lg transition-colors duration-200"
                      >
                        <Play className="h-8 w-8 text-primary-foreground ml-1" fill="currentColor" />
                      </motion.div>

                      {/* Play text */}
                      <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 text-white text-sm font-medium bg-black/50 px-3 py-1 rounded-full">
                        Click to play
                      </div>
                    </motion.div>
                  )}
                </AnimatePresence>
              </div>
            </div>
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  );
}
