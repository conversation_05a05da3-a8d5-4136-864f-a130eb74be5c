'use client';

import * as React from 'react';
import Link from 'next/link';
import { Bot, Menu, Store, Shield, Key, Workflow, Plus } from 'lucide-react';

import { NavAgents } from '@/components/sidebar/nav-agents';
import { NavUserWithTeams } from '@/components/sidebar/nav-user-with-teams';
import { KortixLogo } from '@/components/sidebar/kortix-logo';
import { CTACard } from '@/components/sidebar/cta';
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarGroup,
  SidebarHeader,
  SidebarMenuButton,
  SidebarRail,
  SidebarTrigger,
  useSidebar,
} from '@/components/ui/sidebar';
import { useEffect, useState } from 'react';
import { createClient } from '@/lib/supabase/client';
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import { useIsMobile } from '@/hooks/use-mobile';
import { Badge } from '../ui/badge';
import { cn } from '@/lib/utils';
import { usePathname } from 'next/navigation';
import { useFeatureFlags } from '@/lib/feature-flags';

export function SidebarLeft({
  ...props
}: React.ComponentProps<typeof Sidebar>) {
  const { state, setOpen, setOpenMobile } = useSidebar();
  const isMobile = useIsMobile();
  const [user, setUser] = useState<{
    name: string;
    email: string;
    avatar: string;
  }>({
    name: 'Loading...',
    email: '<EMAIL>',
    avatar: '',
  });

  const pathname = usePathname();
  const { flags, loading: flagsLoading } = useFeatureFlags(['custom_agents', 'agent_marketplace']);
  const customAgentsEnabled = flags.custom_agents;
  const marketplaceEnabled = flags.agent_marketplace;

  useEffect(() => {
    const fetchUserData = async () => {
      const supabase = createClient();
      const { data } = await supabase.auth.getUser();

      if (data.user) {
        setUser({
          name:
            data.user.user_metadata?.name ||
            data.user.email?.split('@')[0] ||
            'User',
          email: data.user.email || '',
          avatar: data.user.user_metadata?.avatar_url || '',
        });
      }
    };

    fetchUserData();
  }, []);

  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if ((event.metaKey || event.ctrlKey) && event.key === 'b') {
        event.preventDefault();
        setOpen(!state.startsWith('expanded'));
        window.dispatchEvent(
          new CustomEvent('sidebar-left-toggled', {
            detail: { expanded: !state.startsWith('expanded') },
          }),
        );
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [state, setOpen]);

  return (
    <Sidebar
      collapsible="icon"
      className="border-r-0 bg-background/95 backdrop-blur-sm [&::-webkit-scrollbar]:hidden [-ms-overflow-style:'none'] [scrollbar-width:'none']"
      {...props}
    >
      <SidebarHeader className="px-2 py-2">
        <div className={cn(
          "flex h-[40px] items-center px-1 relative",
          state === 'collapsed' ? "justify-center" : ""
        )}>
          <Link href="/dashboard">
            <KortixLogo />
          </Link>
          {state !== 'collapsed' && (
            <div className="ml-2 transition-all duration-200 ease-in-out whitespace-nowrap">
            </div>
          )}
          {state !== 'collapsed' && !isMobile && (
            <div className="ml-auto flex items-center gap-2">
              <Tooltip>
                <TooltipTrigger asChild>
                  <SidebarTrigger className="h-8 w-8" />
                </TooltipTrigger>
                <TooltipContent>Toggle sidebar (CMD+B)</TooltipContent>
              </Tooltip>
            </div>
          )}
        </div>
      </SidebarHeader>
      <SidebarContent className="[&::-webkit-scrollbar]:hidden [-ms-overflow-style:'none'] [scrollbar-width:'none']">
        {!flagsLoading && (customAgentsEnabled || marketplaceEnabled) && (
          <SidebarGroup>
            <Link href="/dashboard">
              <SidebarMenuButton className={cn({
                'bg-accent text-accent-foreground font-medium': pathname === '/dashboard',
              })}>
                <Plus className="h-4 w-4 mr-2" />
                <span className="flex items-center justify-between w-full">
                  New Task
                </span>
              </SidebarMenuButton>
            </Link>
            {marketplaceEnabled && (
              <Link href="/marketplace">
                <SidebarMenuButton className={cn({
                  'bg-accent text-accent-foreground font-medium': pathname === '/marketplace',
                })}>
                  <Store className="h-4 w-4 mr-2" />
                  <span className="flex items-center justify-between w-full">
                    Marketplace
                  </span>
                </SidebarMenuButton>
              </Link>
            )}
            {customAgentsEnabled && (
              <Link href="/agents">
                <SidebarMenuButton className={cn({
                  'bg-accent text-accent-foreground font-medium': pathname === '/agents',
                })}>
                  <Bot className="h-4 w-4 mr-2" />
                  <span className="flex items-center justify-between w-full">
                  Agents
                  </span>
                </SidebarMenuButton>
              </Link>
            )}
            {customAgentsEnabled && (
              <Link href="/settings/credentials">
                <SidebarMenuButton className={cn({
                  'bg-accent text-accent-foreground font-medium': pathname === '/settings/credentials',
                })}>
                  <Key className="h-4 w-4 mr-2" />
                  <span className="flex items-center justify-between w-full">
                    Credentials
                  </span>
                </SidebarMenuButton>
              </Link>
            )}
          </SidebarGroup>
        )}
        <NavAgents />
      </SidebarContent>
      {(state !== 'collapsed' || isMobile) && (
        <div className="px-3 py-2">
          <CTACard />
        </div>
      )}
      <SidebarFooter>
        {state === 'collapsed' && !isMobile && (
          <div className="mt-2 flex justify-center">
            <Tooltip>
              <TooltipTrigger asChild>
                <SidebarTrigger className="h-8 w-8" />
              </TooltipTrigger>
              <TooltipContent>Expand sidebar (CMD+B)</TooltipContent>
            </Tooltip>
          </div>
        )}
        <NavUserWithTeams user={user} />
      </SidebarFooter>
      <SidebarRail />
    </Sidebar>
  );
}
