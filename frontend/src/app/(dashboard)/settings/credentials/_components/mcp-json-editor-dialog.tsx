'use client';

import React, { useState, useEffect } from 'react';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent } from '@/components/ui/card';
import { Loader2, Save, Code2, Trash2, List } from 'lucide-react';
import { toast } from 'sonner';
import { getComposioAppIcon } from '@/lib/icon-mapping';

interface MCPConfiguration {
  name: string;
  type: 'json' | 'sse' | 'http';
  config: Record<string, any>;
  enabledTools: string[];
  app_name?: string;
  auth_type?: string;
}

interface MCPJsonEditorDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  mcp: MCPConfiguration | null;
  agentId: string;
  agentName: string;
  onSave: (agentId: string, mcpIndex: number, updatedMcp: MCPConfiguration) => Promise<void>;
  mcpIndex: number;
}

export function MCPJsonEditorDialog({
  open,
  onOpenChange,
  mcp,
  agentId,
  agentName,
  onSave,
  mcpIndex
}: MCPJsonEditorDialogProps) {
  const [isSaving, setIsSaving] = useState(false);
  const [parsedConfig, setParsedConfig] = useState<MCPConfiguration | null>(null);

  useEffect(() => {
    if (mcp && open) {
      setParsedConfig(mcp);
    }
  }, [mcp, open]);



  const handleDeleteTool = (toolName: string) => {
    if (!parsedConfig) return;

    const updatedConfig = {
      ...parsedConfig,
      enabledTools: parsedConfig.enabledTools.filter(tool => tool !== toolName)
    };

    setParsedConfig(updatedConfig);
  };

  const handleSave = async () => {
    if (!parsedConfig) {
      toast.error('No configuration to save');
      return;
    }

    setIsSaving(true);
    try {
      await onSave(agentId, mcpIndex, parsedConfig);
      toast.success('MCP configuration updated successfully');
      onOpenChange(false);
    } catch (error) {
      toast.error(error instanceof Error ? error.message : 'Failed to save configuration');
    } finally {
      setIsSaving(false);
    }
  };

  if (!mcp) return null;

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl h-[80vh] flex flex-col">
        <DialogHeader className="flex-shrink-0">
          <DialogTitle className="flex items-center gap-2">
            <Code2 className="h-5 w-5" />
            Edit MCP Configuration
          </DialogTitle>
          <DialogDescription>
            Editing <strong>{mcp.name}</strong> in agent <strong>{agentName}</strong>
          </DialogDescription>
          <div className="flex items-center gap-2 mt-2">
            <Badge variant="secondary" className="text-xs">
              {mcp.type.toUpperCase()}
            </Badge>
            {mcp.app_name && (
              <Badge variant="outline" className="text-xs">
                {mcp.app_name}
              </Badge>
            )}
          </div>
        </DialogHeader>

        <div className="flex-1 overflow-y-auto">
          {parsedConfig && (
            <div className="space-y-4">
              {/* App Info Card */}
              <Card>
                <CardContent className="p-4">
                  <div className="flex items-center gap-3">
                    <div className="w-12 h-12 flex items-center justify-center bg-background border border-border rounded-lg">
                      {(() => {
                        const appName = parsedConfig.app_name || parsedConfig.name;
                        const IconComponent = getComposioAppIcon({
                          name: appName,
                          key: appName.toLowerCase().replace(/\s+/g, '_'),
                          icon: undefined
                        });
                        return <IconComponent className="h-6 w-6 text-muted-foreground" />;
                      })()}
                    </div>
                    <div className="flex-1">
                      <h3 className="font-semibold text-lg">{parsedConfig.app_name || parsedConfig.name}</h3>
                      <div className="flex items-center gap-2 mt-1">
                        <Badge variant="secondary" className="text-xs">
                          {parsedConfig.type.toUpperCase()}
                        </Badge>
                        <Badge variant="outline" className="text-xs">
                          {parsedConfig.enabledTools.length} tools enabled
                        </Badge>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Enabled Tools */}
              <Card>
                <CardContent className="p-4">
                  <div className="flex items-center gap-2 mb-3">
                    <List className="h-4 w-4" />
                    <h4 className="font-medium">Enabled Tools</h4>
                  </div>
                  {parsedConfig.enabledTools.length > 0 ? (
                    <div className="space-y-2">
                      {parsedConfig.enabledTools.map((tool, index) => (
                        <div key={index} className="flex items-center justify-between p-2 bg-muted/50 rounded-md">
                          <span className="text-sm font-mono">{tool}</span>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleDeleteTool(tool)}
                            className="h-6 w-6 p-0 text-destructive hover:text-destructive hover:bg-destructive/10"
                          >
                            <Trash2 className="h-3 w-3" />
                          </Button>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <p className="text-sm text-muted-foreground">No tools enabled</p>
                  )}
                </CardContent>
              </Card>
            </div>
          )}
        </div>

        <DialogFooter className="gap-2">
          <Button variant="outline" onClick={() => onOpenChange(false)} disabled={isSaving}>
            Cancel
          </Button>
          <Button onClick={handleSave} disabled={!parsedConfig || isSaving}>
            {isSaving ? (
              <>
                <Loader2 className="h-4 w-4 animate-spin mr-2" />
                Saving...
              </>
            ) : (
              <>
                <Save className="h-4 w-4 mr-2" />
                Save Changes
              </>
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
