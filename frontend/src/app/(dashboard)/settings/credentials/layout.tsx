import { isFlagEnabled } from '@/lib/feature-flags';
import { Metadata } from 'next';
import { redirect } from 'next/navigation';

export const metadata: Metadata = {
  title: 'Credentials | Atlas',
  description: 'Create and manage credentials to your services',
  openGraph: {
    title: 'Credentials | Atlas',
    description: 'Create and manage credentials to your services',
    type: 'website',
  },
};

export default async function CredentialsLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return <>{children}</>;
}
