import React, { use<PERSON>emo, useCallback } from 'react';
import { Switch } from '@/components/ui/switch';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { DEFAULT_AGENTPRESS_TOOLS, getToolDisplayName } from '../_data/tools';

// Legacy tool name mappings for backward compatibility
const LEGACY_TOOL_MAPPINGS: Record<string, string> = {
  // Legacy format mappings
  'deployment': 'sb_deploy_tool',
  'web_search': 'web_search_tool',
  'image_tools': 'sb_vision_tool',
  'web_scraping': 'sb_browser_tool',
  'browser_tools': 'sb_browser_tool',
  'shell': 'sb_shell_tool',
  'files': 'sb_files_tool',
  'file_management': 'sb_files_tool',
  'terminal': 'sb_shell_tool',
  'browser': 'sb_browser_tool',
  'vision': 'sb_vision_tool',
  'data_providers': 'data_providers_tool',
  'clado': 'clado_tool',
  'expose': 'sb_expose_tool',
  'port_exposure': 'sb_expose_tool',

  // Additional possible legacy names
  'deploy': 'sb_deploy_tool',
  'search': 'web_search_tool',
  'file': 'sb_files_tool',
  'browser_automation': 'sb_browser_tool',
  'web_browser': 'sb_browser_tool',
  'image': 'sb_vision_tool',
  'vision_tool': 'sb_vision_tool',
  'shell_tool': 'sb_shell_tool',
  'file_tool': 'sb_files_tool',
  'deploy_tool': 'sb_deploy_tool',
  'expose_tool': 'sb_expose_tool',
};

// Function to normalize tool keys from legacy format to new format
const normalizeLegacyTools = (tools: Record<string, any>): Record<string, any> => {
  const normalized: Record<string, any> = {};

  // Initialize with default structure for all expected tools
  Object.keys(DEFAULT_AGENTPRESS_TOOLS).forEach(toolKey => {
    normalized[toolKey] = {
      enabled: false,
      description: DEFAULT_AGENTPRESS_TOOLS[toolKey].description,
      ...normalized[toolKey]
    };
  });

  // First, copy any tools that already match the new format
  Object.keys(DEFAULT_AGENTPRESS_TOOLS).forEach(newKey => {
    if (tools[newKey]) {
      normalized[newKey] = {
        ...normalized[newKey],
        ...tools[newKey]
      };
    }
  });

  // Then, map legacy tools to new format
  Object.entries(tools).forEach(([legacyKey, toolData]) => {
    const newKey = LEGACY_TOOL_MAPPINGS[legacyKey];
    if (newKey && newKey in normalized) {
      normalized[newKey] = {
        ...normalized[newKey],
        ...toolData
      };
    }
  });

  return normalized;
};
import { useDefaultAgentMCPs } from '@/hooks/react-query/agents/use-agents';
import { getMCPIconComponent } from '@/lib/icon-mapping';

interface AgentToolsConfigurationProps {
  tools: Record<string, { enabled: boolean; description: string }>;
  onToolsChange: (tools: Record<string, { enabled: boolean; description: string }>) => void;
  mcps?: Array<{ name: string; qualifiedName: string; config: any; enabledTools?: string[]; isCustom?: boolean; customType?: 'http' | 'sse' }>;
  customMcps?: Array<{ name: string; type: 'http' | 'sse'; config: any; enabledTools: string[] }>;
  onMCPToggle?: (mcpName: string, enabled: boolean, isCustom: boolean) => void;
}

export const AgentToolsConfiguration = ({
  tools,
  onToolsChange,
  mcps = [],
  customMcps = [],
  onMCPToggle
}: AgentToolsConfigurationProps) => {
  // Normalize legacy tool format to new format for backward compatibility
  const normalizedTools = useMemo(() => {
    return normalizeLegacyTools(tools || {});
  }, [tools]);

  // Get default agent MCPs for shared tools
  const { data: defaultMCPs } = useDefaultAgentMCPs();

  const handleToolToggle = (toolName: string, enabled: boolean) => {
    const updatedTools = {
      ...normalizedTools,
      [toolName]: {
        ...normalizedTools[toolName],
        enabled,
        description: normalizedTools[toolName]?.description || DEFAULT_AGENTPRESS_TOOLS[toolName]?.description || ''
      }
    };
    onToolsChange(updatedTools);
  };

  const handleMCPToggle = (mcpName: string, enabled: boolean, isCustom: boolean) => {
    if (onMCPToggle) {
      onMCPToggle(mcpName, enabled, isCustom);
    }
  };

  // Use centralized MCP icon component utility for third-party MCP apps

  // Check if an MCP is currently enabled for this agent
  const isMCPEnabled = useCallback((mcpName: string, isCustom: boolean) => {
    if (isCustom) {
      return customMcps.some(mcp => mcp.name === mcpName);
    } else {
      // For configured MCPs, check if they exist in the current agent's configured MCPs
      return mcps.some(mcp => mcp.name === mcpName);
    }
  }, [mcps, customMcps]);

    // Get configured MCPs from current agent (third type - red "advanced" badges)
  const configuredMCPs = useMemo(() => {
    const configured = [];

    // Add configured MCPs from current agent's configured_mcps column
    mcps.forEach(mcp => {
      // Cast to any to handle type mismatch between DB structure and TypeScript types
      const mcpData = mcp as any;

      // Normalize the MCP data to handle different formats
      const normalizedMCP = {
        // Handle both direct name and nested name structures
        name: mcpData.name || mcpData.displayName || 'Unknown MCP',
        qualifiedName: mcpData.qualifiedName || mcpData.qualified_name || '',
        enabledTools: mcpData.enabledTools || mcpData.enabled_tools || [],
        config: mcpData.config || {},
        isCustom: mcpData.isCustom || false,
        selectedProfileId: mcpData.selectedProfileId || mcpData.selected_profile_id || null
      };

      configured.push({
        name: normalizedMCP.name,
        type: 'configured_mcp' as const,
        isCustom: false,
        toolCount: normalizedMCP.enabledTools.length || 0,
        enabled: true, // These are already enabled since they're in the current agent's mcps array
        originalData: normalizedMCP
      });
    });

    return configured;
  }, [mcps]);

  // Get custom MCPs from default agent (existing blue "app" badges)
  const customMCPs = useMemo(() => {
    if (!defaultMCPs) return [];

    const custom = [];

    // Add custom MCPs (including Composio integrations)
    defaultMCPs.custom_mcps.forEach(mcp => {
      custom.push({
        name: mcp.name,
        type: 'custom_mcp' as const,
        isCustom: true,
        iconComponent: getMCPIconComponent({
          name: mcp.name,
          displayName: mcp.name,
          qualifiedName: (mcp as any).qualifiedName,
          isCustom: true,
          type: 'custom_mcp'
        }),
        toolCount: mcp.enabledTools?.length || 0,
        enabled: isMCPEnabled(mcp.name, true),
        originalData: mcp
      });
          });

    return custom;
  }, [defaultMCPs, isMCPEnabled]);

  const getSelectedToolsCount = (): number => {
    const regularToolsCount = Object.values(normalizedTools).filter(tool => tool.enabled).length;
    const enabledCustomMCPsCount = customMCPs.filter(mcp => mcp.enabled).length;
    return regularToolsCount + enabledCustomMCPsCount;
  };

  const getAllTools = (): Array<[string, any]> => {
    return Object.entries(DEFAULT_AGENTPRESS_TOOLS);
  };

  const getAllConfiguredMCPs = () => {
    return configuredMCPs;
  };

  const getAllCustomMCPs = () => {
    return customMCPs;
  };

  return (
    <Card className='px-0 bg-transparent border-none shadow-none'>
      <CardHeader className='px-0'>
        <div className="flex items-center justify-between">
          <span className="text-sm text-muted-foreground">
            {getSelectedToolsCount()} selected
          </span>
        </div>
      </CardHeader>
      <CardContent className="space-y-6 px-0">

        {/* Apps Section - Custom MCP Tools */}
        {getAllCustomMCPs().length > 0 && (
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <h3 className="text-sm font-medium">Apps</h3>
                <p className="text-xs text-muted-foreground">
                  {getAllCustomMCPs().length} available
                </p>
              </div>
            </div>

            <div className="gap-4 grid grid-cols-1 md:grid-cols-2">
              {getAllCustomMCPs().map((mcp) => (
                <div
                  key={`custom-mcp-${mcp.name}`}
                  className="flex items-center gap-3 p-3 bg-muted/50 rounded-lg border hover:border-border/80 transition-colors"
                >
                  {/* Professional MCP app icon - no background needed */}
                  <div className="w-10 h-10 flex-shrink-0">
                    <mcp.iconComponent className="w-full h-full" />
                  </div>

                  <div className="flex-1 min-w-0">
                    <div className="flex items-center justify-between">
                      <h4 className="font-medium text-sm truncate min-w-0 flex-1">
                        {mcp.name}
                      </h4>
                      <Switch
                        checked={mcp.enabled}
                        onCheckedChange={(checked) => handleMCPToggle(mcp.name, checked, mcp.isCustom)}
                        className="flex-shrink-0 ml-2"
                      />
                    </div>
                  </div>
                </div>
              ))}
            </div>

            <div className="border-t" />
          </div>
        )}

        {/* Agentpress Tools Section */}
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-sm font-medium">Tools</h3>
              <p className="text-xs text-muted-foreground">
                {getAllTools().length} available
              </p>
            </div>
          </div>

          <div className="gap-4 grid grid-cols-1 md:grid-cols-2">
            {getAllTools().map(([toolName, toolInfo]) => (
              <div
                key={toolName}
                className="flex items-start gap-3 p-3 bg-muted/50 rounded-lg border hover:border-border/80 transition-colors"
              >
                <div className={`w-10 h-10 rounded-lg ${toolInfo.color} flex items-center justify-center flex-shrink-0`}>
                  <span className="text-lg">{toolInfo.icon}</span>
                </div>
                <div className="flex-1 min-w-0">
                  <div className="flex items-start justify-between">
                    <div className="flex-1 min-w-0">
                      <h4 className="font-medium text-sm truncate min-w-0">
                        {getToolDisplayName(toolName)}
                      </h4>
                      <p className="text-xs text-muted-foreground mt-1 leading-relaxed">
                        {toolInfo.description}
                      </p>
                    </div>
                    <Switch
                      checked={normalizedTools[toolName]?.enabled || false}
                      onCheckedChange={(checked) => handleToolToggle(toolName, checked)}
                      className="flex-shrink-0 ml-3"
                    />
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

      </CardContent>

      {/* Advanced MCP Servers Section - Below Divider */}
      {getAllConfiguredMCPs().length > 0 && (
        <>
          <div className="px-0 py-4">
            <div className="border-t" />
          </div>
          <CardContent className="px-0 pt-0">
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div>
                  <h3 className="text-sm font-medium">Advanced Servers</h3>
                  <p className="text-xs text-muted-foreground">
                    {getAllConfiguredMCPs().length} configured
                  </p>
                </div>
              </div>

              <div className="gap-4 grid grid-cols-1 md:grid-cols-2">
                {getAllConfiguredMCPs().map((mcp) => (
                  <div
                    key={`configured-mcp-${mcp.name}`}
                    className="flex items-center gap-3 p-3 bg-muted/50 rounded-lg border hover:border-border/80 transition-colors"
                  >
                    {/* No icon for configured MCPs - just show name */}
                    <div className="w-10 h-10 flex-shrink-0 rounded-lg bg-red-100 dark:bg-red-800/50 flex items-center justify-center">
                      <span className="text-sm font-medium text-red-700 dark:text-red-300">
                        {mcp.name.charAt(0).toUpperCase()}
                      </span>
                    </div>

                    <div className="flex-1 min-w-0">
                      <div className="flex items-center justify-between">
                        <h4 className="font-medium text-sm truncate min-w-0">
                          {mcp.name}
                        </h4>
                        <Switch
                          checked={mcp.enabled}
                          onCheckedChange={(checked) => handleMCPToggle(mcp.name, checked, mcp.isCustom)}
                          className="flex-shrink-0 ml-2"
                        />
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </CardContent>
        </>
      )}
    </Card>
  );
};
