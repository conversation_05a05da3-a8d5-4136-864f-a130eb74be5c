export const DEFAULT_AGENTPRESS_TOOLS: Record<string, { enabled: boolean; description: string; icon: string; color: string }> = {
    'sb_shell_tool': { enabled: false, description: 'Unleash terminal power with advanced command execution, script automation, and system orchestration', icon: '💻', color: 'bg-slate-100 dark:bg-slate-800' },
    'sb_files_tool': { enabled: false, description: 'Master file operations with intelligent workspace management and seamless content manipulation', icon: '📁', color: 'bg-blue-100 dark:bg-blue-800/50' },
    'sb_browser_tool': { enabled: false, description: 'Dominate the web with sophisticated automation, intelligent navigation, and dynamic interaction', icon: '🌐', color: 'bg-indigo-100 dark:bg-indigo-800/50' },
    'sb_deploy_tool': { enabled: false, description: 'Revolutionize deployments with lightning-fast automation and enterprise-grade reliability', icon: '🚀', color: 'bg-green-100 dark:bg-green-800/50' },
    'sb_expose_tool': { enabled: false, description: 'Deploy websites instantaneously  and share links with your friends', icon: '🔌', color: 'bg-orange-100 dark:bg-orange-800/20' },
    'web_search_tool': { enabled: false, description: 'Supercharge research with advanced web search, intelligent scraping, and real-time insights', icon: '🔍', color: 'bg-yellow-100 dark:bg-yellow-800/50' },
    'sb_vision_tool': { enabled: false, description: 'Unlock visual intelligence with cutting-edge image processing and advanced content analysis', icon: '👁️', color: 'bg-pink-100 dark:bg-pink-800/50' },
    'data_providers_tool': { enabled: false, description: 'Access premium data streams and powerful APIs for comprehensive information gathering', icon: '🔗', color: 'bg-cyan-100 dark:bg-cyan-800/50' },
    'clado_tool': { enabled: false, description: 'Discover anyone instantly with professional LinkedIn intelligence and enriched contact data', icon: '🔍', color: 'bg-purple-100 dark:bg-purple-800/50' },
};

// Configuration for default agent creation - all legacy tools enabled
export const DEFAULT_AGENT_TOOLS_CONFIG = {
    'sb_shell_tool': {
        enabled: true,
        description: 'Execute terminal commands, run scripts, manage system processes',
    },
    'sb_files_tool': {
        enabled: true,
        description: 'Create, read, edit, and organize files and directories',
    },
    'sb_browser_tool': {
        enabled: true,
        description: 'Navigate websites, interact with web applications, scrape content',
    },
    'sb_deploy_tool': {
        enabled: true,
        description: 'Deploy applications, manage containers, handle CI/CD workflows',
    },
    'sb_expose_tool': {
        enabled: true,
        description: 'Expose local services and ports for testing and development',
    },
    'web_search_tool': {
        enabled: true,
        description: 'Search the internet for current information and research',
    },
    'sb_vision_tool': {
        enabled: true,
        description: 'Process images, analyze visual content, generate visual insights',
    },
    'data_providers_tool': {
        enabled: true,
        description: 'Access external APIs and data sources',
    },
    'clado_tool': {
        enabled: true,
        description: 'Clado integration for enhanced functionality',
    },
};

export const getToolDisplayName = (toolName: string): string => {
    const displayNames: Record<string, string> = {
      'sb_shell_tool': 'Terminal',
      'sb_files_tool': 'File Manager',
      'sb_browser_tool': 'Browser Automation',
      'sb_deploy_tool': 'Deploy Tool',
      'sb_expose_tool': 'Port Exposure',
      'web_search_tool': 'Web Search',
      'sb_vision_tool': 'Image Processing',
      'data_providers_tool': 'Data Providers',
      'clado_tool': 'Find Anyone',
    };

    return displayNames[toolName] || toolName.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
  };
