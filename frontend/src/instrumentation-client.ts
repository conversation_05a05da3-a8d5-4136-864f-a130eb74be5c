import * as Sentry from '@sentry/nextjs';
import { SentryConfig } from './sentry.config';
import posthog from 'posthog-js';

// Initialize Sentry
Sentry.init(SentryConfig);

// Initialize PostHog
if (typeof window !== 'undefined') {
  posthog.init(process.env.NEXT_PUBLIC_POSTHOG_KEY || '', {
    api_host: process.env.NEXT_PUBLIC_POSTHOG_HOST || 'https://app.posthog.com',
  });
}

export const onRouterTransitionStart = Sentry.captureRouterTransitionStart;
