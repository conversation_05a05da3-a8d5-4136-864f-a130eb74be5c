'use client';

import { createMutationHook, createQueryHook } from '@/hooks/use-query';
import {
  createCheckoutSession,
  checkBillingStatus,
  getAvailableModels,
  getUsageLogs,
  CreateCheckoutSessionRequest
} from '@/lib/api';
import { modelKeys, subscriptionKeys, usageKeys } from './keys';
import { useQueryClient } from '@tanstack/react-query';

export const useAvailableModels = createQueryHook(
  modelKeys.available,
  getAvailableModels,
  {
    staleTime: 10 * 60 * 1000,
    refetchOnWindowFocus: false,
  }
);

export const useBillingStatus = createQueryHook(
  ['billing', 'status'],
  checkBillingStatus,
  {
    staleTime: 2 * 60 * 1000,
    refetchOnWindowFocus: true,
  }
);

export const useCreateCheckoutSession = () => {
  const queryClient = useQueryClient();

  return createMutationHook(
    (request: CreateCheckoutSessionRequest) => createCheckoutSession(request),
    {
      onSuccess: (data) => {
        // Invalidate subscription cache to ensure fresh data when user returns
        queryClient.invalidateQueries({ queryKey: subscriptionKeys.details() });
        queryClient.invalidateQueries({ queryKey: ['billing', 'status'] });

        if (data.url) {
          window.location.href = data.url;
        }
      },
      errorContext: {
        operation: 'create checkout session',
        resource: 'billing'
      }
    }
  )();
};

export const useUsageLogs = (page: number = 0, itemsPerPage: number = 1000) => {
  return createQueryHook(
    usageKeys.logs(page, itemsPerPage),
    () => getUsageLogs(page, itemsPerPage),
    {
      staleTime: 5 * 60 * 1000, // 5 minutes
      refetchOnWindowFocus: false,
    }
  )();
};
