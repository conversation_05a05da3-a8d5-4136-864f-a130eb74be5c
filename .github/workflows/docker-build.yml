name: Build, push and deploy

on:
  push:
    branches:
      - main
      - PRODUCTION
  workflow_dispatch:

permissions:
  contents: read
  packages: write

jobs:
  build-and-push:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4

      - name: Get tag name
        shell: bash
        run: |
          if [[ "${GITHUB_REF#refs/heads/}" == "main" ]]; then
            echo "branch=latest" >> $GITHUB_OUTPUT
            echo "environment=staging" >> $GITHUB_OUTPUT
          elif [[ "${GITHUB_REF#refs/heads/}" == "PRODUCTION" ]]; then
            echo "branch=prod" >> $GITHUB_OUTPUT
            echo "environment=prod" >> $GITHUB_OUTPUT
          else
            echo "branch=${GITHUB_HEAD_REF:-${GITHUB_REF#refs/heads/}}" >> $GITHUB_OUTPUT
            echo "environment=staging" >> $GITHUB_OUTPUT
          fi
        id: get_tag_name

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Log in to GitHub Container Registry
        uses: docker/login-action@v3
        with:
          registry: ghcr.io
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}

      - name: Build and push Backend image
        uses: docker/build-push-action@v5
        with:
          context: ./backend
          file: ./backend/Dockerfile
          push: true
          platforms: linux/amd64
          tags: ghcr.io/${{ github.repository }}/suna-backend:${{ steps.get_tag_name.outputs.branch }}
          cache-from: type=gha
          cache-to: type=gha,mode=max

      - name: Deploy to staging
        if: steps.get_tag_name.outputs.environment == 'staging'
        uses: appleboy/ssh-action@v1
        with:
          host: ${{ secrets.STAGING_HOST }}
          username: ${{ secrets.STAGING_USERNAME }}
          key: ${{ secrets.STAGING_KEY }}
          script: |
            cd /home/<USER>/backend
            git pull
            docker compose build
            docker compose up -d

      - name: Deploy to prod
        if: steps.get_tag_name.outputs.environment == 'prod'
        uses: appleboy/ssh-action@v1
        with:
          host: ${{ secrets.PROD_HOST }}
          username: ${{ secrets.PROD_USERNAME }}
          key: ${{ secrets.PROD_KEY }}
          script: |
            cd /home/<USER>/backend
            git pull
            docker compose -f docker-compose.yml -f docker-compose.prod.yml build
            docker compose -f docker-compose.yml -f docker-compose.prod.yml up -d
