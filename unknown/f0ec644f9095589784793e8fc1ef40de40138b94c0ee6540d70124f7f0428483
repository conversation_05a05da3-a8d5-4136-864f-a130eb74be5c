# Composio v3 Migration Guide

This document provides a comprehensive guide for migrating from the current Composio MCP authentication implementation to the new Composio v3 SDK approach.

## Overview

The new Composio v3 implementation provides a cleaner, more robust authentication flow using the official Composio SDK instead of custom MCP server interactions.

### Key Improvements

- **Cleaner SDK**: Uses the official Composio v3 SDK with `new Composio()` initialization
- **Simplified Auth Flow**: Direct OAuth integration with `composio.authConfigs.create()`
- **Better Connection Management**: Use `composio.connectedAccounts.initiate()` for OAuth requests
- **Automatic Completion**: `connRequest.waitForConnection()` for connection establishment
- **Standard Callback**: Uses the official callback URL: `https://backend.composio.dev/api/v3/toolkits/callback`

## New Architecture

```
Frontend → Backend v3 API → Composio v3 SDK → Composio Service → Supabase
```

### Authentication Flow

1. **Frontend** calls `/api/composio-v3/initiate-connection` with `app_key`
2. **Backend** creates auth config using `composio.authConfigs.create()`
3. **Backend** initiates connection with `composio.connectedAccounts.initiate()`
4. **Frontend** redirects user to the returned `redirect_url`
5. **User** completes OAuth on Composio's site
6. **Backend** waits for connection with `connRequest.waitForConnection()`
7. **Backend** stores connection in Supabase under `agents.custom_mcps`

## Migration Steps

### 1. Install Dependencies

```bash
cd backend
pip install composio-core>=0.6.0
```

### 2. Environment Variables

Add to your `.env` file:

```bash
# Composio v3 SDK
COMPOSIO_API_KEY=your_composio_api_key_here
```

### 3. Update Integration Constants

The integration mappings in `backend/constants/composio_mcp_servers.json` remain the same:

```json
{
  "gmail": {
    "integration_id": "096a940c-830d-49f9-be93-c4a5f56fd033",
    "server_id": "3adcafb4-4a7d-4b97-bb26-3be782883e06",
    "name": "Gmail",
    "description": "Connect to Gmail for email management and automation"
  },
  "notion": {
    "integration_id": "04a8a7a5-b159-4f16-82be-4c4b786b3689",
    "server_id": "5b1d27a8-c112-470a-af56-3f1c40991ebe",
    "name": "Notion",
    "description": "Connect to Notion for productivity and note management"
  }
}
```

## API Endpoints

### New v3 Endpoints

| Endpoint                                   | Method | Description                         |
| ------------------------------------------ | ------ | ----------------------------------- |
| `/api/composio-v3/initiate-connection`     | POST   | Start OAuth flow for an integration |
| `/api/composio-v3/check-connection-status` | POST   | Check if connection is established  |
| `/api/composio-v3/user-connections`        | GET    | List user's v3 connections          |
| `/api/composio-v3/connection/{app_key}`    | DELETE | Delete a v3 connection              |
| `/api/composio-v3/supported-integrations`  | GET    | Get supported integrations          |

### Legacy Endpoints (Still Available)

The old `/api/composio-mcp/*` endpoints remain available for backward compatibility.

## Frontend Integration

### New v3 Flow

```typescript
// 1. Initiate connection
const response = await fetch("/api/composio-v3/initiate-connection", {
  method: "POST",
  headers: { "Content-Type": "application/json" },
  body: JSON.stringify({ app_key: "gmail" }),
});

const { redirect_url, connection_request_id } = await response.json();

// 2. Redirect user to OAuth
window.location.href = redirect_url;

// 3. After OAuth completion, check status
const statusResponse = await fetch("/api/composio-v3/check-connection-status", {
  method: "POST",
  headers: { "Content-Type": "application/json" },
  body: JSON.stringify({
    connection_request_id,
    app_key: "gmail",
  }),
});

const { success, connection_id, toolsets } = await statusResponse.json();
```

## Database Changes

### Custom MCP Storage

Connections are now stored in the `agents` table under `custom_mcps` with the following structure:

```json
{
  "qualified_name": "composio/gmail",
  "server_url": "https://backend.composio.dev/api/v3/connectedAccounts/{connection_id}/mcp",
  "server_id": "3adcafb4-4a7d-4b97-bb26-3be782883e06",
  "integration_id": "096a940c-830d-49f9-be93-c4a5f56fd033",
  "connection_id": "conn_abc123",
  "connected_account_id": "acc_xyz789",
  "app_key": "gmail",
  "app_name": "Gmail",
  "description": "Connect to Gmail for email management and automation",
  "toolsets": ["GMAIL_SEND_EMAIL", "GMAIL_FETCH_EMAILS", "GMAIL_CREATE_DRAFT"],
  "auth_type": "composio_v3",
  "created_at": "2024-01-15T10:30:00Z",
  "updated_at": "2024-01-15T10:30:00Z"
}
```

### Migration Query

To migrate existing connections, you can run:

```sql
-- Update existing Composio connections to use v3 format
UPDATE agents
SET custom_mcps = jsonb_set(
  custom_mcps,
  '{auth_type}',
  '"composio_v3"'
)
WHERE custom_mcps::text LIKE '%composio%';
```

## Error Handling

### Common Issues

1. **SDK Import Error**: Ensure `composio-core>=0.6.0` is installed
2. **API Key Missing**: Set `COMPOSIO_API_KEY` in environment
3. **Integration Not Found**: Check `composio_mcp_servers.json` has the app_key
4. **Connection Timeout**: Default timeout is 300 seconds, can be adjusted

### Error Responses

```json
{
  "success": false,
  "app_key": "gmail",
  "error": "Integration configuration not found for gmail",
  "message": "Failed to initiate connection for gmail"
}
```

## Testing

### Manual Testing

1. Start the backend server
2. Call the v3 initiation endpoint
3. Follow the redirect URL
4. Complete OAuth flow
5. Check connection status

### Unit Tests

```python
import pytest
from services.composio_v3_service import composio_v3_service

@pytest.mark.asyncio
async def test_initiate_connection():
    result = await composio_v3_service.initiate_connection("gmail", "user123")
    assert result.success
    assert result.redirect_url is not None
```

## Rollback Plan

If issues arise, you can rollback by:

1. Removing the v3 router from `backend/api.py`
2. Commenting out the v3 service import
3. Using the legacy `/api/composio-mcp/*` endpoints

## Support

For issues with the v3 migration:

1. Check the backend logs for detailed error messages
2. Verify environment variables are set correctly
3. Ensure the Composio API key has the correct permissions
4. Test with a single integration first (e.g., Gmail)

## Performance Notes

- The v3 SDK is more efficient than the previous MCP approach
- Connection establishment typically takes 10-30 seconds
- Background tasks can be used for async connection completion
- Rate limiting is handled by the Composio service
