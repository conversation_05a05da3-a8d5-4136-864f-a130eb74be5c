"""
Composio v3 SDK Integration Service

This service implements the new Composio v3 authentication flow using the updated SDK.
It uses the user's Supabase UUID as the entity ID and pre-configured auth config IDs.

Flow:
1. Initialize Composio client with API key
2. Use user's Supabase UUID as entity ID
3. Initiate OAuth connection using pre-configured auth config IDs
4. Wait for connection establishment
5. Store connection in Supabase under agents as custom_mcp

New v3 Features:
- Clean SDK initialization with new Composio()
- composio.connected_accounts.initiate(entity_id, integration_id) for OAuth requests
- connRequest.wait_until_active() for connection establishment
- Uses user's Supabase UUID as entity ID
- Uses pre-configured auth config IDs from constants
"""

import uuid
import json
import os
import asyncio
from typing import Dict, Any, Optional, List
from dataclasses import dataclass
from datetime import datetime
from supabase import create_client, Client
from utils.logger import logger
from utils.default_agent_config import get_default_agent_config
import composio
from composio.client.exceptions import ComposioClientError

# Supabase configuration
SUPABASE_URL = os.getenv("SUPABASE_URL")
SUPABASE_SERVICE_KEY = os.getenv("SUPABASE_SERVICE_ROLE_KEY")

# Composio v3 configuration
COMPOSIO_API_KEY = os.getenv("COMPOSIO_API_KEY")


@dataclass
class ComposioV3Connection:
    """Result of Composio v3 connection creation"""

    success: bool
    app_key: str
    auth_config_id: Optional[str] = None
    connection_id: Optional[str] = None
    redirect_url: Optional[str] = None
    connection_request_id: Optional[str] = None
    entity_id: Optional[str] = None
    error: Optional[str] = None
    message: Optional[str] = None


@dataclass
class ComposioV3AuthResult:
    """Result of Composio v3 authentication completion"""

    success: bool
    app_key: str
    connection_id: Optional[str] = None
    connected_account_id: Optional[str] = None
    entity_id: Optional[str] = None
    toolsets: Optional[List[str]] = None
    error: Optional[str] = None


class ComposioV3Service:
    """Service for managing Composio v3 SDK integrations"""

    def __init__(self):
        if not SUPABASE_URL or not SUPABASE_SERVICE_KEY:
            raise ValueError("Supabase configuration missing")

        if not COMPOSIO_API_KEY:
            raise ValueError("Composio API key missing")

        self.supabase: Client = create_client(SUPABASE_URL, SUPABASE_SERVICE_KEY)
        self.composio = None
        self._init_composio_client()

    def _init_composio_client(self):
        """Initialize Composio v3 client"""
        try:
            # Import Composio v3 SDK
            from composio import Composio

            self.composio = Composio(api_key=COMPOSIO_API_KEY)
            logger.info("Successfully initialized Composio v3 client")
        except ImportError:
            logger.error(
                "Composio v3 SDK not installed. Please install with: pip install composio-core"
            )
            raise
        except Exception as e:
            logger.error(f"Failed to initialize Composio v3 client: {e}")
            raise

    def _load_integration_config(self, app_key: str) -> Optional[Dict[str, Any]]:
        """Load integration configuration from constants file"""
        try:
            constants_file = os.path.join(
                os.path.dirname(__file__),
                "..",
                "constants",
                "composio_mcp_servers.json",
            )

            with open(constants_file, "r") as f:
                constants = json.load(f)

            return constants.get(app_key)
        except Exception as e:
            logger.error(f"Failed to load integration config for {app_key}: {e}")
            return None

    async def initiate_connection(
        self, app_key: str, user_id: str
    ) -> ComposioV3Connection:
        """
        Initiate OAuth connection request using Composio v3 SDK.

        Uses the user's Supabase UUID as entity ID and pre-configured auth config IDs.

        Args:
            app_key: The app to connect (e.g., "gmail", "notion")
            user_id: The user's Supabase UUID (used as entity ID)

        Returns:
            ComposioV3Connection with redirect_url for OAuth flow
        """
        try:
            # Load integration configuration
            integration_config = self._load_integration_config(app_key)
            if not integration_config:
                return ComposioV3Connection(
                    success=False,
                    app_key=app_key,
                    error=f"Integration configuration not found for {app_key}",
                )

            # Get pre-configured auth config ID
            auth_config_id = integration_config.get("integration_id")
            if not auth_config_id:
                return ComposioV3Connection(
                    success=False,
                    app_key=app_key,
                    error=f"Auth config ID not found for {app_key}",
                )

            logger.info(
                f"Initiating connection for {app_key} with auth config {auth_config_id} and entity {user_id}"
            )

            # Initiate connection using v3 SDK with user's Supabase UUID as entity ID
            conn_request = self.composio.connected_accounts.initiate(
                entity_id=user_id,  # User's Supabase UUID as entity ID
                integration_id=auth_config_id,  # Pre-configured auth config ID
                redirect_url="https://atlasagents.ai/dashboard",  # Post-auth redirect destination
            )

            logger.info(
                f"Successfully initiated connection request for {app_key}: {conn_request.connectedAccountId}"
            )

            return ComposioV3Connection(
                success=True,
                app_key=app_key,
                auth_config_id=auth_config_id,
                connection_request_id=conn_request.connectedAccountId,
                redirect_url=conn_request.redirectUrl,
                entity_id=user_id,
                message=f"Connection request initiated for {app_key}. Please complete authentication at the provided URL.",
            )

        except Exception as e:
            logger.error(f"Failed to initiate connection for {app_key}: {e}")
            return ComposioV3Connection(success=False, app_key=app_key, error=str(e))

    async def wait_for_connection(
        self, connection_request_id: str, app_key: str, user_id: str, timeout: int = 300
    ) -> ComposioV3AuthResult:
        """
        Wait for connection establishment and return connection details.

        Args:
            connection_request_id: The connection request ID from initiate_connection
            app_key: The app key (e.g., "gmail", "notion")
            user_id: The user's Supabase UUID
            timeout: Timeout in seconds (default: 300)

        Returns:
            ComposioV3AuthResult with connection details
        """
        try:
            logger.info(f"Waiting for connection completion: {connection_request_id}")

            # The connection_request_id is actually the connectedAccountId from initiate()
            # We need to check the connection status directly
            # Since wait_until_active is only available on ConnectionRequestModel,
            # we'll poll the connected account status instead

            import time

            start_time = time.time()

            while time.time() - start_time < timeout:
                # Get the connected account by ID
                try:
                    connection = self.composio.connected_accounts.get(
                        connection_request_id
                    )

                    # Check if connection is active/established
                    if hasattr(connection, "status") and connection.status == "ACTIVE":
                        logger.info(f"Connection is now active: {connection.id}")
                        break

                    # Wait a bit before checking again
                    await asyncio.sleep(5)

                except Exception as poll_error:
                    logger.warning(f"Error polling connection status: {poll_error}")
                    await asyncio.sleep(5)
            else:
                # Timeout reached
                logger.warning(
                    f"Connection establishment timed out after {timeout} seconds"
                )
                connection = None

            if connection and connection.id:
                logger.info(f"Connection established for {app_key}: {connection.id}")

                # Get available toolsets for this connection
                toolsets = await self._get_connection_toolsets(connection.id, app_key)

                # Store the connection in Supabase
                stored = await self._store_connection_in_supabase(
                    user_id=user_id,
                    app_key=app_key,
                    connection_id=connection.id,
                    connected_account_id=getattr(
                        connection, "connectedAccountId", connection.id
                    ),
                    entity_id=user_id,
                    toolsets=toolsets,
                )

                if stored:
                    return ComposioV3AuthResult(
                        success=True,
                        app_key=app_key,
                        connection_id=connection.id,
                        connected_account_id=getattr(
                            connection, "connectedAccountId", connection.id
                        ),
                        entity_id=user_id,
                        toolsets=toolsets,
                    )
                else:
                    return ComposioV3AuthResult(
                        success=False,
                        app_key=app_key,
                        entity_id=user_id,
                        error="Failed to store connection in database",
                    )
            else:
                return ComposioV3AuthResult(
                    success=False,
                    app_key=app_key,
                    entity_id=user_id,
                    error="Connection not established within timeout period",
                )

        except Exception as e:
            logger.error(f"Failed to wait for connection {connection_request_id}: {e}")
            return ComposioV3AuthResult(
                success=False, app_key=app_key, entity_id=user_id, error=str(e)
            )

    async def _get_connection_toolsets(
        self, connection_id: str, app_key: str
    ) -> List[str]:
        """Get available toolsets for a connection from Composio SDK"""
        try:
            # Try to get actual tools from Composio SDK
            try:
                # Get the connected account and fetch available actions
                connection = self.composio.connected_accounts.get(connection_id)
                if connection:
                    # Get available actions for this app/integration
                    actions = self.composio.actions.list(
                        tags=[app_key.upper()], enabled_for_account=connection_id
                    )

                    tool_names = []
                    for action in actions:
                        # Extract action name/identifier
                        if hasattr(action, "name"):
                            tool_names.append(action.name)
                        elif hasattr(action, "id"):
                            tool_names.append(action.id)
                        elif hasattr(action, "action_name"):
                            tool_names.append(action.action_name)

                    if tool_names:
                        logger.info(
                            f"Found {len(tool_names)} tools for {app_key} from Composio SDK"
                        )
                        return tool_names

            except Exception as sdk_error:
                logger.warning(f"Failed to get tools from Composio SDK: {sdk_error}")

            # Fallback to comprehensive default toolsets based on app
            comprehensive_toolsets = {
                "gmail": [
                    "GMAIL_DELETE_DRAFT",
                    "GMAIL_DELETE_MESSAGE",
                    "GMAIL_FETCH_EMAILS",
                    "GMAIL_GET_CONTACTS",
                    "GMAIL_LIST_DRAFTS",
                    "GMAIL_MOVE_TO_TRASH",
                    "GMAIL_PATCH_LABEL",
                    "GMAIL_REPLY_TO_THREAD",
                    "GMAIL_SEARCH_PEOPLE",
                    "GMAIL_SEND_DRAFT",
                    "GMAIL_SEND_EMAIL",
                    "GMAIL_FETCH_MESSAGE_BY_MESSAGE_ID",
                    "GMAIL_CREATE_EMAIL_DRAFT",
                    "GMAIL_FETCH_MESSAGE_BY_THREAD_ID",
                    "GMAIL_LIST_THREADS",
                ],
                "notion": [
                    "NOTION_ADD_PAGE_CONTENT",
                    "NOTION_CREATE_DATABASE",
                    "NOTION_CREATE_NOTION_PAGE",
                    "NOTION_DELETE_BLOCK",
                    "NOTION_FETCH_DATA",
                    "NOTION_FETCH_DATABASE",
                    "NOTION_FETCH_ROW",
                    "NOTION_GET_ABOUT_USER",
                    "NOTION_INSERT_ROW_DATABASE",
                    "NOTION_LIST_USERS",
                    "NOTION_QUERY_DATABASE",
                    "NOTION_RETRIEVE_COMMENT",
                    "NOTION_RETRIEVE_DATABASE_PROPERTY",
                    "NOTION_UPDATE_PAGE",
                    "NOTION_UPDATE_SCHEMA_DATABASE",
                    "NOTION_APPEND_BLOCK_CHILDREN",
                    "NOTION_FETCH_NOTION_BLOCK",
                    "NOTION_FETCH_NOTION_CHILD_BLOCK",
                    "NOTION_GET_PAGE_PROPERTY_ACTION",
                    "NOTION_NOTION_UPDATE_BLOCK",
                    "NOTION_SEARCH_NOTION_PAGE",
                ],
                "teams": [
                    "MICROSOFT_TEAMS_CREATE_MEETING",
                    "MICROSOFT_TEAMS_TEAMS_CREATE_CHAT",
                    "MICROSOFT_TEAMS_CHATS_GET_ALL_CHATS",
                    "MICROSOFT_TEAMS_CHATS_GET_ALL_MESSAGES",
                    "MICROSOFT_TEAMS_TEAMS_CREATE_CHANNEL",
                    "MICROSOFT_TEAMS_TEAMS_GET_MESSAGE",
                    "MICROSOFT_TEAMS_TEAMS_LIST",
                    "MICROSOFT_TEAMS_TEAMS_LIST_CHANNELS",
                    "MICROSOFT_TEAMS_TEAMS_LIST_CHAT_MESSAGES",
                    "MICROSOFT_TEAMS_TEAMS_LIST_PEOPLE",
                    "MICROSOFT_TEAMS_TEAMS_POST_CHANNEL_MESSAGE",
                    "MICROSOFT_TEAMS_TEAMS_POST_CHAT_MESSAGE",
                    "MICROSOFT_TEAMS_TEAMS_POST_MESSAGE_REPLY",
                ],
                "twitter": [
                    "TWITTER_CREATE_LIST",
                    "TWITTER_CREATION_OF_A_POST",
                    "TWITTER_DELETE_LIST",
                    "TWITTER_FOLLOW_USER",
                    "TWITTER_GET_BLOCKED_USERS",
                    "TWITTER_POST_DELETE_BY_POST_ID",
                    "TWITTER_UNFOLLOW_USER",
                    "TWITTER_USER_LOOKUP_BY_USERNAME",
                    "TWITTER_USER_LOOKUP_ME",
                    "TWITTER_CREATE_A_NEW_DM_CONVERSATION",
                    "TWITTER_RETWEET_POST",
                    "TWITTER_USER_LIKE_POST",
                    "TWITTER_FOLLOW_A_LIST",
                ],
                "linear": [
                    "LINEAR_REMOVE_ISSUE_LABEL",
                    "LINEAR_UPDATE_ISSUE",
                    "LINEAR_CREATE_LINEAR_COMMENT",
                    "LINEAR_CREATE_LINEAR_ISSUE",
                    "LINEAR_CREATE_LINEAR_LABEL",
                    "LINEAR_DELETE_LINEAR_ISSUE",
                    "LINEAR_GET_LINEAR_ISSUE",
                    "LINEAR_LIST_LINEAR_CYCLES",
                    "LINEAR_LIST_LINEAR_ISSUES",
                    "LINEAR_LIST_LINEAR_LABELS",
                    "LINEAR_LIST_LINEAR_PROJECTS",
                    "LINEAR_LIST_LINEAR_STATES",
                    "LINEAR_LIST_LINEAR_TEAMS",
                    "LINEAR_RUN_QUERY_OR_MUTATION",
                    "LINEAR_CREATE_LINEAR_ATTACHMENT",
                ],
                "google_docs": [
                    "GOOGLEDOCS_COPY_DOCUMENT",
                    "GOOGLEDOCS_CREATE_FOOTER",
                    "GOOGLEDOCS_CREATE_HEADER",
                    "GOOGLEDOCS_CREATE_NAMED_RANGE",
                    "GOOGLEDOCS_CREATE_PARAGRAPH_BULLETS",
                    "GOOGLEDOCS_DELETE_CONTENT_RANGE",
                    "GOOGLEDOCS_DELETE_FOOTER",
                    "GOOGLEDOCS_DELETE_HEADER",
                    "GOOGLEDOCS_DELETE_NAMED_RANGE",
                    "GOOGLEDOCS_DELETE_PARAGRAPH_BULLETS",
                    "GOOGLEDOCS_DELETE_TABLE",
                    "GOOGLEDOCS_DELETE_TABLE_COLUMN",
                    "GOOGLEDOCS_DELETE_TABLE_ROW",
                    "GOOGLEDOCS_GET_CHARTS_FROM_SPREADSHEET",
                    "GOOGLEDOCS_INSERT_PAGE_BREAK",
                    "GOOGLEDOCS_INSERT_TABLE_ACTION",
                    "GOOGLEDOCS_INSERT_TEXT_ACTION",
                    "GOOGLEDOCS_REPLACE_ALL_TEXT",
                    "GOOGLEDOCS_UPDATE_DOCUMENT_STYLE",
                    "GOOGLEDOCS_CREATE_DOCUMENT",
                    "GOOGLEDOCS_GET_DOCUMENT_BY_ID",
                    "GOOGLEDOCS_SEARCH_DOCUMENTS",
                ],
                "slack": [
                    "SLACK_ADD_REACTION_TO_AN_ITEM",
                    "SLACK_CREATE_A_REMINDER",
                    "SLACK_FETCH_CONVERSATION_HISTORY",
                    "SLACK_LIST_ALL_SLACK_TEAM_CHANNELS_WITH_VARIOUS_FILTERS",
                    "SLACK_LIST_ALL_SLACK_TEAM_USERS_WITH_PAGINATION",
                    "SLACK_REMOVE_REACTION_FROM_ITEM",
                    "SLACK_SCHEDULES_A_MESSAGE_TO_A_CHANNEL_AT_A_SPECIFIED_TIME",
                    "SLACK_SEARCH_FOR_MESSAGES_WITH_QUERY",
                    "SLACK_SENDS_A_MESSAGE_TO_A_SLACK_CHANNEL",
                    "SLACK_UPDATES_A_SLACK_MESSAGE",
                    "SLACK_CONVERSATIONS_HISTORY",
                    "SLACK_CONVERSATIONS_INFO",
                    "SLACK_CONVERSATIONS_LIST",
                    "SLACK_USERS_INFO",
                    "SLACK_USERS_PROFILE_GET_PROFILE_INFO",
                    "SLACK_LISTS_PINNED_ITEMS_IN_A_CHANNEL",
                ],
                "google_calendar": [
                    "GOOGLECALENDAR_PATCH_EVENT",
                    "GOOGLECALENDAR_CALENDARS_UPDATE",
                    "GOOGLECALENDAR_CLEAR_CALENDAR",
                    "GOOGLECALENDAR_CREATE_EVENT",
                    "GOOGLECALENDAR_DELETE_EVENT",
                    "GOOGLECALENDAR_DUPLICATE_CALENDAR",
                    "GOOGLECALENDAR_EVENTS_INSTANCES",
                    "GOOGLECALENDAR_EVENTS_LIST",
                    "GOOGLECALENDAR_EVENTS_MOVE",
                    "GOOGLECALENDAR_FIND_EVENT",
                    "GOOGLECALENDAR_GET_CALENDAR",
                    "GOOGLECALENDAR_GET_CURRENT_DATE_TIME",
                    "GOOGLECALENDAR_LIST_CALENDARS",
                    "GOOGLECALENDAR_QUICK_ADD",
                    "GOOGLECALENDAR_REMOVE_ATTENDEE",
                    "GOOGLECALENDAR_UPDATE_EVENT",
                    "GOOGLECALENDAR_FIND_FREE_SLOTS",
                ],
                "google_sheets": [
                    "GOOGLESHEETS_ADD_SHEET",
                    "GOOGLESHEETS_CLEAR_BASIC_FILTER",
                    "GOOGLESHEETS_DELETE_SHEET",
                    "GOOGLESHEETS_GET_SPREADSHEET_BY_DATA_FILTER",
                    "GOOGLESHEETS_GET_SPREADSHEET_INFO",
                    "GOOGLESHEETS_INSERT_DIMENSION",
                    "GOOGLESHEETS_SET_BASIC_FILTER",
                    "GOOGLESHEETS_SPREADSHEETS_VALUES_APPEND",
                    "GOOGLESHEETS_SPREADSHEETS_VALUES_BATCH_CLEAR",
                    "GOOGLESHEETS_SPREADSHEETS_VALUES_BATCH_GET_BY_DATA_FILTER",
                    "GOOGLESHEETS_UPDATE_SPREADSHEET_PROPERTIES",
                    "GOOGLESHEETS_BATCH_GET",
                    "GOOGLESHEETS_BATCH_UPDATE",
                    "GOOGLESHEETS_BATCH_UPDATE_VALUES_BY_DATA_FILTER",
                    "GOOGLESHEETS_CLEAR_VALUES",
                    "GOOGLESHEETS_CREATE_GOOGLE_SHEET1",
                    "GOOGLESHEETS_FIND_WORKSHEET_BY_TITLE",
                    "GOOGLESHEETS_FORMAT_CELL",
                    "GOOGLESHEETS_SEARCH_SPREADSHEETS",
                ],
                "airtable": [
                    "AIRTABLE_LIST_BASES",
                    "AIRTABLE_CREATE_BASE",
                    "AIRTABLE_CREATE_COMMENT",
                    "AIRTABLE_CREATE_FIELD",
                    "AIRTABLE_CREATE_MULTIPLE_RECORDS",
                    "AIRTABLE_CREATE_RECORD",
                    "AIRTABLE_CREATE_TABLE",
                    "AIRTABLE_DELETE_COMMENT",
                    "AIRTABLE_DELETE_MULTIPLE_RECORDS",
                    "AIRTABLE_DELETE_RECORD",
                    "AIRTABLE_GET_BASE_SCHEMA",
                    "AIRTABLE_GET_RECORD",
                    "AIRTABLE_GET_USER_INFO",
                    "AIRTABLE_LIST_COMMENTS",
                    "AIRTABLE_LIST_RECORDS",
                    "AIRTABLE_UPDATE_MULTIPLE_RECORDS",
                    "AIRTABLE_UPDATE_RECORD",
                ],
                "reddit": [
                    "REDDIT_EDIT_REDDIT_COMMENT_OR_POST",
                    "REDDIT_CREATE_REDDIT_POST",
                    "REDDIT_DELETE_REDDIT_COMMENT",
                    "REDDIT_DELETE_REDDIT_POST",
                    "REDDIT_GET_USER_FLAIR",
                    "REDDIT_POST_REDDIT_COMMENT",
                    "REDDIT_RETRIEVE_POST_COMMENTS",
                    "REDDIT_RETRIEVE_REDDIT_POST",
                    "REDDIT_RETRIEVE_SPECIFIC_COMMENT",
                    "REDDIT_SEARCH_ACROSS_SUBREDDITS",
                ],
                "outlook": [
                    "OUTLOOK_DOWNLOAD_OUTLOOK_ATTACHMENT",
                    "OUTLOOK_OUTLOOK_CALENDAR_CREATE_EVENT",
                    "OUTLOOK_OUTLOOK_CREATE_CONTACT",
                    "OUTLOOK_OUTLOOK_CREATE_DRAFT",
                    "OUTLOOK_OUTLOOK_GET_CONTACT",
                    "OUTLOOK_OUTLOOK_GET_EVENT",
                    "OUTLOOK_OUTLOOK_GET_PROFILE",
                    "OUTLOOK_OUTLOOK_LIST_EVENTS",
                    "OUTLOOK_OUTLOOK_LIST_MESSAGES",
                    "OUTLOOK_OUTLOOK_REPLY_EMAIL",
                    "OUTLOOK_OUTLOOK_SEND_EMAIL",
                    "OUTLOOK_OUTLOOK_UPDATE_EMAIL",
                    "OUTLOOK_LIST_OUTLOOK_ATTACHMENTS",
                    "OUTLOOK_OUTLOOK_CREATE_DRAFT_REPLY",
                    "OUTLOOK_OUTLOOK_DELETE_CONTACT",
                    "OUTLOOK_OUTLOOK_DELETE_EVENT",
                    "OUTLOOK_OUTLOOK_GET_MESSAGE",
                    "OUTLOOK_OUTLOOK_GET_SCHEDULE",
                    "OUTLOOK_OUTLOOK_LIST_CONTACTS",
                    "OUTLOOK_OUTLOOK_SEARCH_MESSAGES",
                    "OUTLOOK_OUTLOOK_UPDATE_CALENDAR_EVENT",
                    "OUTLOOK_OUTLOOK_UPDATE_CONTACT",
                ],
                "hubspot": [
                    "HUBSPOT_CREATE_PRODUCT_BATCH",
                    "HUBSPOT_CREATE_PRODUCT_OBJECT",
                    "HUBSPOT_LIST_PRODUCTS_WITH_PAGING",
                    "HUBSPOT_UPDATE_PRODUCT",
                    "HUBSPOT_CAMPAIGN_SEARCH",
                    "HUBSPOT_CREATE_A_BATCH_OF_COMPANIES",
                    "HUBSPOT_CREATE_A_BATCH_OF_CONTACTS",
                    "HUBSPOT_CREATE_A_BATCH_OF_EMAILS",
                    "HUBSPOT_CREATE_BATCH_OF_DEALS",
                    "HUBSPOT_CREATE_WORKFLOW",
                    "HUBSPOT_CUSTOMIZABLE_CONTACTS_PAGE_RETRIEVAL",
                    "HUBSPOT_FETCH_CONTACT_DETAILS_BY_ID",
                    "HUBSPOT_FETCH_CONTACT_IDS",
                    "HUBSPOT_GET_ALL_WORKFLOWS",
                    "HUBSPOT_GET_CAMPAIGN_METRICS",
                    "HUBSPOT_LIST",
                    "HUBSPOT_PARTIALLY_UPDATE_CONTACT_USING_CONTACT_ID",
                    "HUBSPOT_UPDATE",
                    "HUBSPOT_UPDATE_A_BATCH_OF_CAMPAIGNS",
                    "HUBSPOT_UPDATE_A_BATCH_OF_CONTACTS",
                    "HUBSPOT_UPDATE_A_BATCH_OF_EMAILS",
                ],
                "zoom": [
                    "ZOOM_CREATE_A_MEETING",
                    "ZOOM_GET_A_MEETING",
                    "ZOOM_GET_A_MEETING_SUMMARY",
                    "ZOOM_GET_MEETING_RECORDINGS",
                    "ZOOM_LIST_ALL_RECORDINGS",
                    "ZOOM_LIST_MEETINGS",
                    "ZOOM_UPDATE_MEETING_STATUS",
                    "ZOOM_ADD_A_MEETING_REGISTRANT",
                    "ZOOM_GET_MEETING_INVITATION",
                    "ZOOM_GET_MEETING_RECORDING_S_ANALYTICS_SUMMARY",
                ],
                "salesforce": [
                    "SALESFORCE_ACCOUNT_CREATION_WITH_CONTENT_TYPE_OPTION",
                    "SALESFORCE_CREATE_CAMPAIGN_RECORD_VIA_POST",
                    "SALESFORCE_CREATE_LEAD_WITH_SPECIFIED_CONTENT_TYPE",
                    "SALESFORCE_CREATE_NOTE_RECORD_WITH_CONTENT_TYPE_HEADER",
                    "SALESFORCE_EXECUTE_SOQL_QUERY",
                    "SALESFORCE_FETCH_MODIFIED_OR_UNMODIFIED_SOBJECTS",
                    "SALESFORCE_QUERY_CONTACTS_BY_NAME",
                    "SALESFORCE_REMOVE_ACCOUNT_BY_UNIQUE_IDENTIFIER",
                    "SALESFORCE_RETRIEVE_ACCOUNT_DATA_AND_ERROR_RESPONSES",
                    "SALESFORCE_RETRIEVE_CAMPAIGN_DATA_WITH_ERROR_HANDLING",
                    "SALESFORCE_RETRIEVE_LEAD_BY_ID",
                    "SALESFORCE_RETRIEVE_LEAD_DATA_WITH_VARIOUS_RESPONSES",
                    "SALESFORCE_RETRIEVE_NOTE_WITH_CONDITIONS",
                    "SALESFORCE_RETRIEVE_OPPORTUNITIES_DATA",
                    "SALESFORCE_RETRIEVE_SPECIFIC_CONTACT_BY_ID",
                    "SALESFORCE_UPDATE_CONTACT_BY_ID",
                ],
                "google_drive": [
                    "GOOGLEDRIVE_CREATE_COMMENT",
                    "GOOGLEDRIVE_CREATE_DRIVE",
                    "GOOGLEDRIVE_CREATE_FILE",
                    "GOOGLEDRIVE_CREATE_FOLDER",
                    "GOOGLEDRIVE_DOWNLOAD_FILE",
                    "GOOGLEDRIVE_GET_PERMISSION",
                    "GOOGLEDRIVE_GET_REPLY",
                    "GOOGLEDRIVE_LIST_CHANGES",
                    "GOOGLEDRIVE_LIST_FILES",
                    "GOOGLEDRIVE_LIST_SHARED_DRIVES",
                    "GOOGLEDRIVE_MOVE_FILE",
                    "GOOGLEDRIVE_PARSE_FILE",
                    "GOOGLEDRIVE_UPLOAD_FILE",
                    "GOOGLEDRIVE_GENERATE_IDS",
                    "GOOGLEDRIVE_COPY_FILE",
                    "GOOGLEDRIVE_CREATE_FILE_FROM_TEXT",
                    "GOOGLEDRIVE_EDIT_FILE",
                    "GOOGLEDRIVE_FIND_FILE",
                    "GOOGLEDRIVE_FIND_FOLDER",
                    "GOOGLEDRIVE_GOOGLE_DRIVE_DELETE_FOLDER_OR_FILE_ACTION",
                ],
                "clickup": [
                    "CLICKUP_CREATE_FOLDER",
                    "CLICKUP_CREATE_LIST",
                    "CLICKUP_CREATE_TASK",
                    "CLICKUP_CREATE_TASK_COMMENT",
                    "CLICKUP_DELETE_TASK",
                    "CLICKUP_GET_TASK",
                    "CLICKUP_UPDATE_TASK",
                    "CLICKUP_AUTHORIZATION_VIEW_ACCOUNT_DETAILS",
                    "CLICKUP_CREATE_CHECKLIST",
                    "CLICKUP_CREATE_CHECKLIST_ITEM",
                    "CLICKUP_CREATE_GOAL",
                    "CLICKUP_CREATE_KEY_RESULT",
                    "CLICKUP_CREATE_SPACE",
                    "CLICKUP_CREATE_TEAM",
                    "CLICKUP_EDIT_CHECKLIST",
                    "CLICKUP_FOLDERS_GET_CONTENTS_OF",
                    "CLICKUP_GET_ACCESS_TOKEN",
                    "CLICKUP_GET_GOALS",
                    "CLICKUP_GET_GUEST",
                    "CLICKUP_GET_LIST_MEMBERS",
                    "CLICKUP_GET_TASKS",
                    "CLICKUP_GET_TEAMS",
                ],
            }

            tools = comprehensive_toolsets.get(app_key, [])
            logger.info(f"Using {len(tools)} default tools for {app_key}")
            return tools

        except Exception as e:
            logger.error(f"Failed to get toolsets for connection {connection_id}: {e}")
            return []

    async def _store_connection_in_supabase(
        self,
        user_id: str,
        app_key: str,
        connection_id: str,
        connected_account_id: str,
        entity_id: str,
        toolsets: List[str],
    ) -> bool:
        """Store the v3 connection in Supabase under agents as custom_mcp"""
        try:
            # Get or create default agent for user
            agent = await self._get_or_create_default_agent(user_id)
            if not agent:
                logger.error(f"Failed to get/create default agent for user {user_id}")
                return False

            # Load integration config
            integration_config = self._load_integration_config(app_key)
            if not integration_config:
                logger.error(f"Integration config not found for {app_key}")
                return False

            # Create custom MCP entry for the v3 connection in the proper MCP server format
            # URL format: https://mcp.composio.dev/composio/server/{server_id}/mcp?user_id={entity_id}
            server_id = integration_config.get("server_id")
            mcp_url = f"https://mcp.composio.dev/composio/server/{server_id}/mcp?user_id={entity_id}"

            custom_mcp_entry = {
                "name": integration_config.get("name", app_key.title()),
                "type": "http",
                "config": {"url": mcp_url},
                "enabledTools": toolsets,
                # Include app_key for deletion logic compatibility
                "app_key": app_key,
                # Store additional metadata for potential future use (hidden from MCP)
                "_metadata": {
                    "auth_type": "composio_v3",
                    "connection_id": connection_id,
                    "connected_account_id": connected_account_id,
                    "entity_id": entity_id,
                    "server_id": server_id,
                    "auth_config_id": integration_config.get("integration_id"),
                    "qualified_name": f"composio/{app_key}",
                    "description": integration_config.get(
                        "description", f"{app_key.title()} integration"
                    ),
                    "created_at": datetime.utcnow().isoformat(),
                    "updated_at": datetime.utcnow().isoformat(),
                },
            }

            # Get existing custom MCPs
            existing_mcps = agent.get("custom_mcps", [])
            if not isinstance(existing_mcps, list):
                existing_mcps = []

            # Remove existing connection for same app_key if exists
            existing_mcps = [
                mcp for mcp in existing_mcps if mcp.get("app_key") != app_key
            ]

            # Add new connection
            existing_mcps.append(custom_mcp_entry)

            # Update agent with new custom MCPs
            result = (
                self.supabase.table("agents")
                .update(
                    {
                        "custom_mcps": existing_mcps,
                    }
                )
                .eq("agent_id", agent["agent_id"])
                .execute()
            )

            if result.data:
                logger.info(
                    f"Successfully stored v3 connection for {app_key} with entity ID {entity_id}"
                )
                return True
            else:
                logger.error(f"Failed to update agent with v3 connection for {app_key}")
                return False

        except Exception as e:
            logger.error(f"Failed to store v3 connection in Supabase: {e}")
            return False

    async def _get_or_create_default_agent(
        self, user_id: str
    ) -> Optional[Dict[str, Any]]:
        """Get or create default agent for user"""
        try:
            # First try to get existing default agent
            # Note: agents table uses account_id (same as user's Supabase UUID)
            result = (
                self.supabase.table("agents")
                .select("*")
                .eq("account_id", user_id)
                .eq("is_default", True)
                .execute()
            )

            if result.data:
                return result.data[0]

            # If no default agent exists, create one using the standard configuration
            agent_data = get_default_agent_config(user_id)

            result = self.supabase.table("agents").insert(agent_data).execute()

            if result.data:
                logger.info(f"Created default agent for user {user_id}")
                return result.data[0]
            else:
                logger.error(f"Failed to create default agent for user {user_id}")
                return None

        except Exception as e:
            logger.error(f"Failed to get/create default agent for user {user_id}: {e}")
            return None

    async def list_user_connections(self, user_id: str) -> List[Dict[str, Any]]:
        """List all Composio v3 connections for a user"""
        try:
            # Get user's default agent
            agent = await self._get_or_create_default_agent(user_id)
            if not agent:
                return []

            # Filter custom MCPs for Composio v3 connections
            custom_mcps = agent.get("custom_mcps", [])
            if not isinstance(custom_mcps, list):
                return []

            v3_connections = [
                mcp
                for mcp in custom_mcps
                if (
                    mcp.get("auth_type") == "composio_v3"
                    or mcp.get("_metadata", {}).get("auth_type") == "composio_v3"
                )
            ]

            logger.info(
                f"Found {len(v3_connections)} v3 connections for user {user_id}"
            )
            return v3_connections

        except Exception as e:
            logger.error(f"Failed to list user v3 connections: {e}")
            return []

    async def delete_user_connection(self, user_id: str, app_key: str) -> bool:
        """Delete a Composio v3 connection for a user"""
        try:
            # Get user's default agent
            agent = await self._get_or_create_default_agent(user_id)
            if not agent:
                return False

            # Remove the connection from custom MCPs
            custom_mcps = agent.get("custom_mcps", [])
            if not isinstance(custom_mcps, list):
                custom_mcps = []

            updated_mcps = [mcp for mcp in custom_mcps if mcp.get("app_key") != app_key]

            # Update agent
            result = (
                self.supabase.table("agents")
                .update(
                    {
                        "custom_mcps": updated_mcps,
                    }
                )
                .eq("agent_id", agent["agent_id"])
                .execute()
            )

            if result.data:
                logger.info(f"Successfully deleted v3 connection for {app_key}")
                return True
            else:
                logger.error(f"Failed to delete v3 connection for {app_key}")
                return False

        except Exception as e:
            logger.error(f"Failed to delete user v3 connection: {e}")
            return False

    async def get_supported_integrations(self) -> List[Dict[str, Any]]:
        """Get list of supported integrations from constants file"""
        try:
            constants_file = os.path.join(
                os.path.dirname(__file__),
                "..",
                "constants",
                "composio_mcp_servers.json",
            )

            with open(constants_file, "r") as f:
                constants = json.load(f)

            integrations = []
            for app_key, config in constants.items():
                integrations.append(
                    {
                        "app_key": app_key,
                        "name": config.get("name", app_key.title()),
                        "description": config.get(
                            "description", f"{app_key.title()} integration"
                        ),
                        "auth_config_id": config.get("integration_id"),
                        "server_id": config.get("server_id"),
                    }
                )

            return integrations

        except Exception as e:
            logger.error(f"Failed to get supported integrations: {e}")
            return []


# Global service instance
composio_v3_service = ComposioV3Service()
