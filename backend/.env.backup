# Generated by Suna setup script

# Environment Mode
# Valid values: local, staging, production
ENV_MODE=production

#DATABASE
SUPABASE_URL=https://twmdhuwpsgvqbbbekzdu.supabase.co
SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.oqmWpSe-QFLYexLrgkwiqiCFvRnkCKwZz2vN_dHRvi0
SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************************************************************.xpSxGLZifcdTxfOxjvEb2deboTWgpbrPDAvKPqGoXdw

# # REDIS
# REDIS_HOST=redis
# REDIS_PORT=6379
# REDIS_PASSWORD=
# REDIS_SSL=false

# REDIS (Production - Upstash)
REDIS_HOST=crucial-doe-44410.upstash.io
REDIS_PORT=6379
REDIS_PASSWORD=Aa16AAIjcDE4NDVlNDUwOThkMGQ0Y2YyYWRkNmRjNTQyYjY5NTI4ZHAxMA
REDIS_SSL=true

# # RABBITMQ
# RABBITMQ_HOST=rabbitmq
# RABBITMQ_PORT=5672

# LLM Providers:
OPENROUTER_API_KEY=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
MODEL_TO_USE=openai/o3
ANTHROPIC_API_KEY=************************************************************************************************************
GROQ_API_KEY=

# AWS Bedrock
AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_REGION_NAME=

# DATA APIS
RAPID_API_KEY=3d8b68b4a5msh1c0934fa2966cf7p1b997ajsn04a192e5122b

# WEB SEARCH
TAVILY_API_KEY=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

# WEB SCRAPE
FIRECRAWL_API_KEY=fc-6afac5a800be4c648637dc1d33691a14
FIRECRAWL_URL=https://api.firecrawl.dev

# Sandbox container provider:
DAYTONA_API_KEY=dtn_538b9e25a53beea967ccc8f9bf781d46e7371b514bd6da4cff0d0c020bf83591
DAYTONA_SERVER_URL=https://app.daytona.io/api
DAYTONA_TARGET=us
NEXT_PUBLIC_URL=http://localhost:3000

SMITHERY_API_KEY=774f49e6-5d8c-4765-a81d-e0042f83472d

# Stripe Configuration (Production)
STRIPE_SECRET_KEY=***********************************************************************************************************
STRIPE_PUBLISHABLE_KEY=pk_live_51RO25fGKgx4qnTxJ90cRRfMtpnFpHkOA4pjGP559yllxlAm7AOUPz6OO2TCnjK32fC339soUoghQceOpkJC0UPl000WNovhsG7
STRIPE_WEBHOOK_SECRET=whsec_Kk2XUEDnnkZbAAYMaAN2lgmCeP8YsARf
STRIPE_PRODUCT_ID=prod_SV0cYYCWXJqSmO

QSTASH_URL="https://qstash.upstash.io"
QSTASH_TOKEN="eyJVc2VySUQiOiIzZTVhNzA2MC05NDM4LTQyNjMtOTEyNC00MWJkNTE5ZTkyMDkiLCJQYXNzd29yZCI6IjZkMWY3ZmY3NzFjNzQ5ODU5YzQ2MGIwMjRlYTAwMWIzIn0="
QSTASH_CURRENT_SIGNING_KEY="sig_88Q1GZjp4nWYauSQXKjk8Ev7cEL5"
QSTASH_NEXT_SIGNING_KEY="sig_6rMchvkvC4x8yWRemuCEC38UwRTX"

# Test Stripe Configuration

#STRIPE_SECRET_KEY=sk_test_51RO25fGKgx4qnTxJeTa6zt7ifmQ5IwrWlomCnglhBgOZ5dKCKzTpwD2Vwtajzt8RT3zrHpOaztJ3i1ACD2Tn1X2w000KodXjAk
#STRIPE_PUBLISHABLE_KEY=pk_test_51RO25fGKgx4qnTxJFZjphGwglabGOlmw6qrWUeEdKavGCzxL3uT4oh1oCQRne614LDGbnMpcf8fF7LcvSFWsRHiA00t6VWnRqz
#STRIPE_WEBHOOK_SECRET=whsec_58c91e4dadd25dadb86d73d74d2b5b09f4338f845298f5801ec7212d8fad1986
#STRIPE_PRODUCT_ID=prod_SV3rLT7uMPOEIr
OPENAI_API_KEY=**************************************************************************************************************************************************************************************************************************

# # Generated by Suna setup script
# # Environment Mode
# # Valid values: local, staging, production
# ENV_MODE=production
# STRIPE_SECRET_KEY=***********************************************************************************************************

# STRIPE_PUBLISHABLE_KEY=pk_live_51RO25fGKgx4qnTxJ90cRRfMtpnFpHkOA4pjGP559yllxlAm7AOUPz6OO2TCnjK32fC339soUoghQceOpkJC0UPl000WNovhsG7

# #DATABASE
# SUPABASE_URL=https://twmdhuwpsgvqbbbekzdu.supabase.co
# SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.oqmWpSe-QFLYexLrgkwiqiCFvRnkCKwZz2vN_dHRvi0
# SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************************************************************.xpSxGLZifcdTxfOxjvEb2deboTWgpbrPDAvKPqGoXdw

# # REDIS (Production - Upstash)
# REDIS_HOST=crucial-doe-44410.upstash.io
# REDIS_PORT=6379
# REDIS_PASSWORD=Aa16AAIjcDE4NDVlNDUwOThkMGQ0Y2YyYWRkNmRjNTQyYjY5NTI4ZHAxMA
# REDIS_SSL=true

# # RABBITMQ (Production - CloudAMQP)
RABBITMQ_HOST=leopard.lmq.cloudamqp.com
RABBITMQ_PORT=5671
RABBITMQ_USER=vbbonmuj
RABBITMQ_PASSWORD=mFCcvxD-2Elbwsb9j6piqRNrcUayzzIf
RABBITMQ_VHOST=vbbonmuj
RABBITMQ_URL=amqps://vbbonmuj:<EMAIL>/vbbonmuj

# # LLM Providers:
# OPENROUTER_API_KEY=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
# MODEL_TO_USE=openai/o3
# ANTHROPIC_API_KEY=************************************************************************************************************
#
# GROQ_API_KEY=

# # AWS Bedrock
# AWS_ACCESS_KEY_ID=
# AWS_SECRET_ACCESS_KEY=
# AWS_REGION_NAME=

# # DATA APIS
# RAPID_API_KEY=3d8b68b4a5msh1c0934fa2966cf7p1b997ajsn04a192e5122b

# # WEB SEARCH
# TAVILY_API_KEY=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

# # WEB SCRAPE
# FIRECRAWL_API_KEY=fc-6afac5a800be4c648637dc1d33691a14
# FIRECRAWL_URL=https://api.firecrawl.dev

# # Sandbox container provider:
# DAYTONA_API_KEY=dtn_538b9e25a53beea967ccc8f9bf781d46e7371b514bd6da4cff0d0c020bf83591
# DAYTONA_SERVER_URL=https://app.daytona.io/api
# DAYTONA_TARGET=us
# NEXT_PUBLIC_URL=https://yourdomain.com

# SMITHERY_API_KEY=774f49e6-5d8c-4765-a81d-e0042f83472d



GOOGLE_CLIENT_ID=233306363987-smv1gh0k4hga3bunevnq52essbjdu4h7.apps.googleusercontent.com
GOOGLE_CLIENT_SECRET=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
OPENROUTER_API_BASE=https://openrouter.ai/api/v1

CLOUDFLARE_API_TOKEN=6a79dc4691ddd8769d98985f6c0195cc55e44

# CLADO API (LinkedIn Data Search & Enrichment)
CLADO_API_KEY=lk_24b76dd8667c4fb08e5f6f56572597c1

MCP_CREDENTIAL_ENCRYPTION_KEY="XHQYazu2Ht0kcDSyViNp8atAkUePgmvXkB6Yp2IxhTE="

# Fake Stripe Price IDs for Development
STRIPE_FREE_TIER_ID_PROD=price_fake_free_prod
STRIPE_TIER_2_20_ID_PROD=price_fake_tier_2_20_prod
STRIPE_TIER_6_50_ID_PROD=price_fake_tier_6_50_prod
STRIPE_TIER_12_100_ID_PROD=price_fake_tier_12_100_prod
STRIPE_TIER_25_200_ID_PROD=price_fake_tier_25_200_prod
STRIPE_TIER_50_400_ID_PROD=price_fake_tier_50_400_prod
STRIPE_TIER_125_800_ID_PROD=price_fake_tier_125_800_prod
STRIPE_TIER_200_1000_ID_PROD=price_fake_tier_200_1000_prod

# Fake Stripe Price IDs for Staging
STRIPE_FREE_TIER_ID_STAGING=price_fake_free_staging
STRIPE_TIER_2_20_ID_STAGING=price_fake_tier_2_20_staging
STRIPE_TIER_6_50_ID_STAGING=price_fake_tier_6_50_staging
STRIPE_TIER_12_100_ID_STAGING=price_fake_tier_12_100_staging
STRIPE_TIER_25_200_ID_STAGING=price_fake_tier_25_200_staging
STRIPE_TIER_50_400_ID_STAGING=price_fake_tier_50_400_staging
STRIPE_TIER_125_800_ID_STAGING=price_fake_tier_125_800_staging
STRIPE_TIER_200_1000_ID_STAGING=price_fake_tier_200_1000_staging

# Fake Stripe Product IDs
STRIPE_PRODUCT_ID_PROD=prod_fake_prod
STRIPE_PRODUCT_ID_STAGING=prod_fake_staging

COMPOSIO_API_KEY=kgqlx4cs6yatvvvnxhyh
