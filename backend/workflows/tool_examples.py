TOOL_XML_EXAMPLES = {
    "computer_use_tool": '<function_calls>\n        <invoke name="move_to">\n        <parameter name="x">100</parameter>\n        <parameter name="y">200</parameter>\n        </invoke>\n        </function_calls>\n\n<function_calls>\n        <invoke name="click">\n        <parameter name="x">100</parameter>\n        <parameter name="y">200</parameter>\n        <parameter name="button">left</parameter>\n        <parameter name="num_clicks">1</parameter>\n        </invoke>\n        </function_calls>\n\n<function_calls>\n        <invoke name="scroll">\n        <parameter name="amount">-3</parameter>\n        </invoke>\n        </function_calls>\n\n<function_calls>\n        <invoke name="typing">\n        <parameter name="text">Hello World!</parameter>\n        </invoke>\n        </function_calls>\n\n<function_calls>\n        <invoke name="press">\n        <parameter name="key">enter</parameter>\n        </invoke>\n        </function_calls>\n\n<function_calls>\n        <invoke name="wait">\n        <parameter name="duration">1.5</parameter>\n        </invoke>\n        </function_calls>\n\n<function_calls>\n        <invoke name="mouse_down">\n        <parameter name="button">left</parameter>\n        </invoke>\n        </function_calls>\n\n<function_calls>\n        <invoke name="mouse_up">\n        <parameter name="button">left</parameter>\n        </invoke>\n        </function_calls>\n\n<function_calls>\n        <invoke name="drag_to">\n        <parameter name="x">500</parameter>\n        <parameter name="y">50</parameter>\n        </invoke>\n        </function_calls>\n\n<function_calls>\n        <invoke name="hotkey">\n        <parameter name="keys">ctrl+a</parameter>\n        </invoke>\n        </function_calls>',

    "data_providers_tool": '<!-- \nThe get-data-provider-endpoints tool returns available endpoints for a specific data provider.\nUse this tool when you need to discover what endpoints are available.\n-->\n\n<!-- Example to get LinkedIn API endpoints -->\n<function_calls>\n<invoke name="get_data_provider_endpoints">\n<parameter name="service_name">linkedin</parameter>\n</invoke>\n</function_calls>\n\n<!-- \n        The execute-data-provider-call tool makes a request to a specific data provider endpoint.\n        Use this tool when you need to call an data provider endpoint with specific parameters.\n        The route must be a valid endpoint key obtained from get-data-provider-endpoints tool!!\n        -->\n        \n        <!-- Example to call linkedIn service with the specific route person -->\n        <function_calls>\n        <invoke name="execute_data_provider_call">\n        <parameter name="service_name">linkedin</parameter>\n        <parameter name="route">person</parameter>\n        <parameter name="payload">{"link": "https://www.linkedin.com/in/johndoe/"}</parameter>\n        </invoke>\n        </function_calls>',

    "expand_msg_tool": '<!-- Example 1: Expand a message that was truncated in the previous conversation -->\n        <function_calls>\n        <invoke name="expand_message">\n        <parameter name="message_id">ecde3a4c-c7dc-4776-ae5c-8209517c5576</parameter>\n        </invoke>\n        </function_calls>\n\n        <!-- Example 2: Expand a message to create reports or analyze truncated data -->\n        <function_calls>\n        <invoke name="expand_message">\n        <parameter name="message_id">f47ac10b-58cc-4372-a567-0e02b2c3d479</parameter>\n        </invoke>\n        </function_calls>\n\n        <!-- Example 3: Expand a message when you need the full content for analysis -->\n        <function_calls>\n        <invoke name="expand_message">\n        <parameter name="message_id">550e8400-e29b-41d4-a716-446655440000</parameter>\n        </invoke>\n        </function_calls>',

    "mcp_tool_wrapper": '<function_calls>\n        <invoke name="call_mcp_tool">\n        <parameter name="tool_name">mcp_exa_web_search_exa</parameter>\n        <parameter name="arguments">{"query": "latest developments in AI", "num_results": 10}</parameter>\n        </invoke>\n        </function_calls>',

    "message_tool": '<function_calls>\n        <invoke name="ask">\n        <parameter name="text">I\'m planning to bake the chocolate cake for your birthday party. The recipe mentions "rich frosting" but doesn\'t specify what type. Could you clarify your preferences? For example:\n1. Would you prefer buttercream or cream cheese frosting?\n2. Do you want any specific flavor added to the frosting (vanilla, coffee, etc.)?\n3. Should I add any decorative toppings like sprinkles or fruit?\n4. Do you have any dietary restrictions I should be aware of?\n\nThis information will help me make sure the cake meets your expectations for the celebration.</parameter>\n        <parameter name="attachments">recipes/chocolate_cake.txt,photos/cake_examples.jpg</parameter>\n        </invoke>\n        </function_calls>\n\n<function_calls>\n        <invoke name="web_browser_takeover">\n        <parameter name="text">I\'ve encountered a CAPTCHA verification on the page. Please:\n1. Solve the CAPTCHA puzzle\n2. Let me know once you\'ve completed it\n3. I\'ll then continue with the automated process\n\nIf you encounter any issues or need to take additional steps, please let me know.</parameter>\n        </invoke>\n        </function_calls>\n\n<function_calls>\n        <invoke name="complete">\n        </invoke>\n        </function_calls>',

    "sb_browser_tool": '<function_calls>\n        <invoke name="browser_navigate_to">\n        <parameter name="url">https://example.com</parameter>\n        </invoke>\n        </function_calls>\n\n<function_calls>\n        <invoke name="browser_go_back">\n        </invoke>\n        </function_calls>\n\n<function_calls>\n        <invoke name="browser_wait">\n        <parameter name="seconds">5</parameter>\n        </invoke>\n        </function_calls>\n\n<function_calls>\n        <invoke name="browser_click_element">\n        <parameter name="index">2</parameter>\n        </invoke>\n        </function_calls>\n\n<function_calls>\n        <invoke name="browser_input_text">\n        <parameter name="index">2</parameter>\n        <parameter name="text">Hello, world!</parameter>\n        </invoke>\n        </function_calls>\n\n<function_calls>\n        <invoke name="browser_send_keys">\n        <parameter name="keys">Enter</parameter>\n        </invoke>\n        </function_calls>\n\n<function_calls>\n        <invoke name="browser_switch_tab">\n        <parameter name="page_id">1</parameter>\n        </invoke>\n        </function_calls>\n\n<function_calls>\n        <invoke name="browser_close_tab">\n        <parameter name="page_id">1</parameter>\n        </invoke>\n        </function_calls>\n\n<function_calls>\n        <invoke name="browser_scroll_down">\n        <parameter name="amount">500</parameter>\n        </invoke>\n        </function_calls>\n\n<function_calls>\n        <invoke name="browser_scroll_up">\n        <parameter name="amount">500</parameter>\n        </invoke>\n        </function_calls>\n\n<function_calls>\n        <invoke name="browser_scroll_to_text">\n        <parameter name="text">Contact Us</parameter>\n        </invoke>\n        </function_calls>\n\n<function_calls>\n        <invoke name="browser_get_dropdown_options">\n        <parameter name="index">2</parameter>\n        </invoke>\n        </function_calls>\n\n<function_calls>\n        <invoke name="browser_select_dropdown_option">\n        <parameter name="index">2</parameter>\n        <parameter name="text">Option 1</parameter>\n        </invoke>\n        </function_calls>\n\n<function_calls>\n        <invoke name="browser_drag_drop">\n        <parameter name="element_source">#draggable</parameter>\n        <parameter name="element_target">#droppable</parameter>\n        </invoke>\n        </function_calls>\n\n<function_calls>\n        <invoke name="browser_click_coordinates">\n        <parameter name="x">100</parameter>\n        <parameter name="y">200</parameter>\n        </invoke>\n        </function_calls>',

    "sb_deploy_tool": '<!-- \n        IMPORTANT: Only use this tool when:\n        1. The user explicitly requests permanent deployment to production\n        2. You have a complete, ready-to-deploy directory \n        \n        NOTE: If the same name is used, it will redeploy to the same project as before\n        -->\n\n        <function_calls>\n        <invoke name="deploy">\n        <parameter name="name">my-site</parameter>\n        <parameter name="directory_path">website</parameter>\n        </invoke>\n        </function_calls>',

    "sb_expose_tool": '<!-- Example 1: Expose a web server running on port 8000 -->\n        <function_calls>\n        <invoke name="expose_port">\n        <parameter name="port">8000</parameter>\n        </invoke>\n        </function_calls>\n\n        <!-- Example 2: Expose an API service running on port 3000 -->\n        <function_calls>\n        <invoke name="expose_port">\n        <parameter name="port">3000</parameter>\n        </invoke>\n        </function_calls>\n\n        <!-- Example 3: Expose a development server running on port 5173 -->\n        <function_calls>\n        <invoke name="expose_port">\n        <parameter name="port">5173</parameter>\n        </invoke>\n        </function_calls>',

    "sb_files_tool": '<function_calls>\n        <invoke name="create_file">\n        <parameter name="file_path">src/main.py</parameter>\n        <parameter name="file_contents">\n        # This is the file content\n        def main():\n            print("Hello, World!")\n        \n        if __name__ == "__main__":\n            main()\n        </parameter>\n        </invoke>\n        </function_calls>\n\n<function_calls>\n        <invoke name="str_replace">\n        <parameter name="file_path">src/main.py</parameter>\n        <parameter name="old_str">text to replace (must appear exactly once in the file)</parameter>\n        <parameter name="new_str">replacement text that will be inserted instead</parameter>\n        </invoke>\n        </function_calls>\n\n<function_calls>\n        <invoke name="full_file_rewrite">\n        <parameter name="file_path">src/main.py</parameter>\n        <parameter name="file_contents">\n        This completely replaces the entire file content.\n        Use when making major changes to a file or when the changes\n        are too extensive for str-replace.\n        All previous content will be lost and replaced with this text.\n        </parameter>\n        </invoke>\n        </function_calls>\n\n<function_calls>\n        <invoke name="delete_file">\n        <parameter name="file_path">src/main.py</parameter>\n        </invoke>\n        </function_calls>',

    "sb_shell_tool": '<function_calls>\n        <invoke name="execute_command">\n        <parameter name="command">npm run dev</parameter>\n        <parameter name="session_name">dev_server</parameter>\n        </invoke>\n        </function_calls>\n\n        <!-- Example 2: Running in Specific Directory -->\n        <function_calls>\n        <invoke name="execute_command">\n        <parameter name="command">npm run build</parameter>\n        <parameter name="folder">frontend</parameter>\n        <parameter name="session_name">build_process</parameter>\n        </invoke>\n        </function_calls>\n\n        <!-- Example 3: Blocking command (wait for completion) -->\n        <function_calls>\n        <invoke name="execute_command">\n        <parameter name="command">npm install</parameter>\n        <parameter name="blocking">true</parameter>\n        <parameter name="timeout">300</parameter>\n        </invoke>\n        </function_calls>\n\n<function_calls>\n        <invoke name="check_command_output">\n        <parameter name="session_name">dev_server</parameter>\n        </invoke>\n        </function_calls>\n        \n        <!-- Example 2: Check final output and kill session -->\n        <function_calls>\n        <invoke name="check_command_output">\n        <parameter name="session_name">build_process</parameter>\n        <parameter name="kill_session">true</parameter>\n        </invoke>\n        </function_calls>\n\n<function_calls>\n        <invoke name="terminate_command">\n        <parameter name="session_name">dev_server</parameter>\n        </invoke>\n        </function_calls>\n\n<function_calls>\n        <invoke name="list_commands">\n        </invoke>\n        </function_calls>',

    "sb_vision_tool": '<!-- Example: Request to see an image named \'diagram.png\' inside the \'docs\' folder -->\n        <function_calls>\n        <invoke name="see_image">\n        <parameter name="file_path">docs/diagram.png</parameter>\n        </invoke>\n        </function_calls>',

    "update_agent_tool": '<function_calls>\n        <invoke name="update_agent">\n        <parameter name="name">Research Assistant</parameter>\n        <parameter name="description">An AI assistant specialized in conducting research and providing comprehensive analysis</parameter>\n        <parameter name="system_prompt">You are a research assistant with expertise in gathering, analyzing, and synthesizing information. Your approach is thorough and methodical...</parameter>\n        <parameter name="agentpress_tools">{"web_search": {"enabled": true, "description": "Search the web for information"}, "sb_files": {"enabled": true, "description": "Read and write files"}}</parameter>\n        <parameter name="avatar">🔬</parameter>\n        <parameter name="avatar_color">#4F46E5</parameter>\n        </invoke>\n        </function_calls>\n\n<function_calls>\n        <invoke name="get_current_agent_config">\n        </invoke>\n        </function_calls>\n\n<function_calls>\n        <invoke name="search_mcp_servers">\n        <parameter name="query">linear</parameter>\n        <parameter name="limit">5</parameter>\n        </invoke>\n        </function_calls>\n\n<function_calls>\n        <invoke name="get_mcp_server_tools">\n        <parameter name="qualified_name">exa</parameter>\n        </invoke>\n        </function_calls>\n\n<function_calls>\n        <invoke name="configure_mcp_server">\n        <parameter name="qualified_name">exa</parameter>\n        <parameter name="display_name">Exa Search</parameter>\n        <parameter name="enabled_tools">["search", "find_similar"]</parameter>\n        <parameter name="config">{"exaApiKey": "user-api-key"}</parameter>\n        </invoke>\n        </function_calls>\n\n<function_calls>\n        <invoke name="get_popular_mcp_servers">\n        <parameter name="category">AI & Search</parameter>\n        </invoke>\n        </function_calls>\n\n<function_calls>\n        <invoke name="test_mcp_server_connection">\n        <parameter name="qualified_name">exa</parameter>\n        <parameter name="config">{"exaApiKey": "user-api-key"}</parameter>\n        </invoke>\n        </function_calls>',

    "web_search_tool": '<function_calls>\n        <invoke name="web_search">\n        <parameter name="query">what is Kortix AI and what are they building?</parameter>\n        <parameter name="num_results">20</parameter>\n        </invoke>\n        </function_calls>\n        \n        <!-- Another search example -->\n        <function_calls>\n        <invoke name="web_search">\n        <parameter name="query">latest AI research on transformer models</parameter>\n        <parameter name="num_results">20</parameter>\n        </invoke>\n        </function_calls>\n\n<function_calls>\n        <invoke name="scrape_webpage">\n        <parameter name="urls">https://www.kortix.ai/,https://github.com/kortix-ai/suna</parameter>\n        </invoke>\n        </function_calls>',

}

def get_tool_xml_example(tool_id: str) -> str:
    if tool_id in TOOL_XML_EXAMPLES:
        return TOOL_XML_EXAMPLES[tool_id]
    if tool_id not in TOOL_XML_EXAMPLES and '_' in tool_id:
        return f'<function_calls>\n        <invoke name="{tool_id}">\n        <parameter name="param1">value1</parameter>\n        <parameter name="param2">value2</parameter>\n        </invoke>\n        </function_calls>'
    return ""

def get_tools_xml_examples(tool_ids: list) -> str:
    examples = []
    for tool_id in tool_ids:
        example = get_tool_xml_example(tool_id)
        if example:
            examples.append(f"## {tool_id.replace('_', ' ').title()}")
            examples.append(example)
            examples.append("")
    
    return "\n".join(examples)

def get_all_available_tools() -> list:
    return list(TOOL_XML_EXAMPLES.keys())
